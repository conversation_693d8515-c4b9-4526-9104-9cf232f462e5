# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)

# Load the data
df = pd.read_csv('kickstarter_projects (3) (1).csv')

# Display basic information about the dataset
print("Dataset Shape:", df.shape)
print("\nColumn Names:")
print(df.columns.tolist())
print("\nFirst few rows:")
df.head()

# Calculate overall statistics
total_projects = len(df)
overall_success_rate = (len(df[df['State'] == 'Successful']) / total_projects) * 100

print(f"Total projects in dataset: {total_projects:,}")
print(f"Overall success rate: {overall_success_rate:.2f}%")
print(f"\nProject state distribution:")
print(df['State'].value_counts())

# Analyze categories by project count and success rate
category_analysis = df.groupby('Category').agg({
    'State': ['count', lambda x: (x == 'Successful').sum()]
}).round(2)

category_analysis.columns = ['Total_Projects', 'Successful_Projects']
category_analysis['Success_Rate'] = (category_analysis['Successful_Projects'] / category_analysis['Total_Projects']) * 100
category_analysis['Failed_Projects'] = category_analysis['Total_Projects'] - category_analysis['Successful_Projects']
category_analysis['Project_Share'] = (category_analysis['Total_Projects'] / total_projects) * 100

# Sort by total projects (descending)
category_analysis = category_analysis.sort_values('Total_Projects', ascending=False)

print("📊 CATEGORY ANALYSIS (sorted by project count):")
print("=" * 80)
print(f"{'Category':<15} {'Total':<8} {'Success':<8} {'Failed':<8} {'Success%':<9} {'Share%':<7}")
print("-" * 80)

for category, row in category_analysis.iterrows():
    total = int(row['Total_Projects'])
    successful = int(row['Successful_Projects'])
    failed = int(row['Failed_Projects'])
    success_rate = row['Success_Rate']
    share = row['Project_Share']
    print(f"{category:<15} {total:<8,} {successful:<8,} {failed:<8,} {success_rate:<9.2f} {share:<7.2f}")

# Identify categories with high volume but low success rate
# Define criteria: 
# 1. High volume: Above median project count
# 2. Low success: Below overall success rate

median_projects = category_analysis['Total_Projects'].median()
high_volume_categories = category_analysis[category_analysis['Total_Projects'] > median_projects]
low_success_categories = category_analysis[category_analysis['Success_Rate'] < overall_success_rate]

# Categories that are both high volume AND low success
high_volume_low_success = category_analysis[
    (category_analysis['Total_Projects'] > median_projects) & 
    (category_analysis['Success_Rate'] < overall_success_rate)
]

print(f"📈 HIGH VOLUME, LOW SUCCESS CATEGORIES:")
print(f"Criteria: > {median_projects:.0f} projects AND < {overall_success_rate:.2f}% success rate")
print("=" * 80)

if len(high_volume_low_success) > 0:
    # Sort by total projects to find the most problematic
    high_volume_low_success_sorted = high_volume_low_success.sort_values('Total_Projects', ascending=False)
    
    print(f"{'Category':<15} {'Total':<8} {'Success':<8} {'Success%':<9} {'Gap':<8}")
    print("-" * 60)
    
    for category, row in high_volume_low_success_sorted.iterrows():
        total = int(row['Total_Projects'])
        successful = int(row['Successful_Projects'])
        success_rate = row['Success_Rate']
        gap = overall_success_rate - success_rate
        print(f"{category:<15} {total:<8,} {successful:<8,} {success_rate:<9.2f} {gap:<8.2f}")
        
    # Identify the worst offender
    worst_category = high_volume_low_success_sorted.index[0]
    worst_stats = high_volume_low_success_sorted.iloc[0]
    
    print(f"\n🎯 ANSWER: Category with most projects but lowest success rate:")
    print(f"'{worst_category}' - {int(worst_stats['Total_Projects']):,} projects, {worst_stats['Success_Rate']:.2f}% success rate")
    print(f"This is {worst_stats['Success_Rate'] - overall_success_rate:.2f} percentage points below the overall average.")
else:
    print("No categories found that meet both criteria.")

# Alternative analysis: Calculate a "disappointment index"
# This combines high volume with low success rate
category_analysis['Volume_Rank'] = category_analysis['Total_Projects'].rank(ascending=False)
category_analysis['Success_Rank'] = category_analysis['Success_Rate'].rank(ascending=True)  # Lower success = higher rank
category_analysis['Disappointment_Index'] = (category_analysis['Volume_Rank'] + category_analysis['Success_Rank']) / 2

# Sort by disappointment index (lower is worse)
disappointment_ranking = category_analysis.sort_values('Disappointment_Index', ascending=True)

print("😞 DISAPPOINTMENT INDEX RANKING:")
print("(Lower index = High volume + Low success rate)")
print("=" * 75)
print(f"{'Rank':<4} {'Category':<15} {'Projects':<8} {'Success%':<9} {'Index':<8}")
print("-" * 75)

for i, (category, row) in enumerate(disappointment_ranking.head(10).iterrows(), 1):
    total = int(row['Total_Projects'])
    success_rate = row['Success_Rate']
    index = row['Disappointment_Index']
    print(f"{i:<4} {category:<15} {total:<8,} {success_rate:<9.2f} {index:<8.1f}")

# Detailed analysis of the most problematic categories
top_3_disappointing = disappointment_ranking.head(3)

print("🔍 DETAILED ANALYSIS OF TOP 3 MOST DISAPPOINTING CATEGORIES:")
print("=" * 70)

for i, (category, row) in enumerate(top_3_disappointing.iterrows(), 1):
    total = int(row['Total_Projects'])
    successful = int(row['Successful_Projects'])
    failed = int(row['Failed_Projects'])
    success_rate = row['Success_Rate']
    share = row['Project_Share']
    
    print(f"\n{i}. {category.upper()}:")
    print(f"   • Total projects: {total:,} ({share:.1f}% of all projects)")
    print(f"   • Successful: {successful:,} projects")
    print(f"   • Failed: {failed:,} projects")
    print(f"   • Success rate: {success_rate:.2f}%")
    print(f"   • Gap from overall average: {success_rate - overall_success_rate:.2f} percentage points")
    
    # Calculate potential impact if this category had average success rate
    potential_successful = int(total * (overall_success_rate / 100))
    missed_opportunities = potential_successful - successful
    print(f"   • Missed opportunities: {missed_opportunities:,} projects could have succeeded with average success rate")

# Create comprehensive visualizations
fig, axes = plt.subplots(2, 2, figsize=(20, 16))

# 1. Scatter plot: Project Count vs Success Rate
ax1 = axes[0, 0]
scatter = ax1.scatter(category_analysis['Total_Projects'], category_analysis['Success_Rate'], 
                     s=100, alpha=0.7, c=category_analysis['Disappointment_Index'], 
                     cmap='RdYlBu_r')

# Add category labels for the most disappointing ones
for category, row in disappointment_ranking.head(5).iterrows():
    ax1.annotate(category, (row['Total_Projects'], row['Success_Rate']), 
                xytext=(5, 5), textcoords='offset points', fontsize=9,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))

ax1.axhline(y=overall_success_rate, color='red', linestyle='--', alpha=0.7, 
           label=f'Overall Success Rate ({overall_success_rate:.1f}%)')
ax1.axvline(x=median_projects, color='orange', linestyle='--', alpha=0.7, 
           label=f'Median Project Count ({median_projects:.0f})')
ax1.set_xlabel('Total Projects')
ax1.set_ylabel('Success Rate (%)')
ax1.set_title('Project Volume vs Success Rate by Category', fontweight='bold')
ax1.legend()
ax1.grid(True, alpha=0.3)
plt.colorbar(scatter, ax=ax1, label='Disappointment Index')

# 2. Bar chart: Top 10 categories by project count with success rates
ax2 = axes[0, 1]
top_10_volume = category_analysis.head(10)
x_pos = range(len(top_10_volume))

bars = ax2.bar(x_pos, top_10_volume['Total_Projects'], 
               color=['red' if rate < overall_success_rate else 'green' 
                     for rate in top_10_volume['Success_Rate']], alpha=0.7)

ax2.set_xticks(x_pos)
ax2.set_xticklabels(top_10_volume.index, rotation=45, ha='right')
ax2.set_ylabel('Number of Projects')
ax2.set_title('Top 10 Categories by Project Count\n(Red = Below Average Success Rate)', fontweight='bold')

# Add success rate labels on bars
for i, (bar, rate) in enumerate(zip(bars, top_10_volume['Success_Rate'])):
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height + 500,
             f'{rate:.1f}%', ha='center', va='bottom', fontsize=9)

# 3. Disappointment index ranking
ax3 = axes[1, 0]
top_10_disappointing = disappointment_ranking.head(10)
bars3 = ax3.barh(range(len(top_10_disappointing)), top_10_disappointing['Disappointment_Index'], 
                 color='lightcoral', alpha=0.7)
ax3.set_yticks(range(len(top_10_disappointing)))
ax3.set_yticklabels(top_10_disappointing.index)
ax3.set_xlabel('Disappointment Index (Lower = More Disappointing)')
ax3.set_title('Top 10 Most Disappointing Categories', fontweight='bold')
ax3.invert_yaxis()

# Add value labels
for i, bar in enumerate(bars3):
    width = bar.get_width()
    ax3.text(width + 0.1, bar.get_y() + bar.get_height()/2,
             f'{width:.1f}', ha='left', va='center', fontsize=9)

# 4. Success rate comparison
ax4 = axes[1, 1]
success_comparison = category_analysis.sort_values('Success_Rate')
colors = ['red' if projects > median_projects else 'lightblue' 
          for projects in success_comparison['Total_Projects']]

bars4 = ax4.barh(range(len(success_comparison)), success_comparison['Success_Rate'], 
                 color=colors, alpha=0.7)
ax4.set_yticks(range(len(success_comparison)))
ax4.set_yticklabels(success_comparison.index, fontsize=8)
ax4.set_xlabel('Success Rate (%)')
ax4.set_title('Success Rate by Category\n(Red = High Volume Categories)', fontweight='bold')
ax4.axvline(x=overall_success_rate, color='black', linestyle='--', alpha=0.7, 
           label=f'Overall Average ({overall_success_rate:.1f}%)')
ax4.legend()

plt.tight_layout()
plt.show()

# Analyze subcategories within the most disappointing category
most_disappointing_category = disappointment_ranking.index[0]
subcategory_data = df[df['Category'] == most_disappointing_category]

subcategory_analysis = subcategory_data.groupby('Subcategory').agg({
    'State': ['count', lambda x: (x == 'Successful').sum()]
}).round(2)

subcategory_analysis.columns = ['Total_Projects', 'Successful_Projects']
subcategory_analysis['Success_Rate'] = (subcategory_analysis['Successful_Projects'] / subcategory_analysis['Total_Projects']) * 100
subcategory_analysis = subcategory_analysis.sort_values('Total_Projects', ascending=False)

print(f"🔍 SUBCATEGORY BREAKDOWN FOR '{most_disappointing_category.upper()}':")
print("=" * 65)
print(f"{'Subcategory':<25} {'Total':<8} {'Success':<8} {'Success%':<9}")
print("-" * 65)

for subcategory, row in subcategory_analysis.head(10).iterrows():
    total = int(row['Total_Projects'])
    successful = int(row['Successful_Projects'])
    success_rate = row['Success_Rate']
    print(f"{subcategory:<25} {total:<8,} {successful:<8,} {success_rate:<9.2f}")

# Find the most problematic subcategory
if len(subcategory_analysis) > 0:
    # Filter subcategories with at least 10 projects for meaningful analysis
    significant_subcats = subcategory_analysis[subcategory_analysis['Total_Projects'] >= 10]
    if len(significant_subcats) > 0:
        worst_subcat = significant_subcats.sort_values('Success_Rate').index[0]
        worst_subcat_stats = significant_subcats.loc[worst_subcat]
        print(f"\n📉 Most challenging subcategory: '{worst_subcat}'")
        print(f"   {int(worst_subcat_stats['Total_Projects'])} projects, {worst_subcat_stats['Success_Rate']:.2f}% success rate")

# Final summary and insights
print("=" * 80)
print("🎯 FINAL SUMMARY: CATEGORIES WITH HIGH VOLUME BUT LOW SUCCESS")
print("=" * 80)

if len(high_volume_low_success) > 0:
    worst_category = high_volume_low_success_sorted.index[0]
    worst_stats = high_volume_low_success_sorted.iloc[0]
    
    print(f"\n🏆 MAIN ANSWER:")
    print(f"'{worst_category}' has the most projects but lowest success rate among high-volume categories.")
    
    print(f"\n📊 KEY STATISTICS:")
    print(f"• Total projects: {int(worst_stats['Total_Projects']):,}")
    print(f"• Successful projects: {int(worst_stats['Successful_Projects']):,}")
    print(f"• Success rate: {worst_stats['Success_Rate']:.2f}%")
    print(f"• Share of all projects: {worst_stats['Project_Share']:.2f}%")
    print(f"• Gap from overall average: {worst_stats['Success_Rate'] - overall_success_rate:.2f} percentage points")
    
    # Calculate the impact
    total_failed = int(worst_stats['Failed_Projects'])
    potential_successful = int(worst_stats['Total_Projects'] * (overall_success_rate / 100))
    missed_opportunities = potential_successful - int(worst_stats['Successful_Projects'])
    
    print(f"\n💔 IMPACT ANALYSIS:")
    print(f"• Failed projects: {total_failed:,}")
    print(f"• Missed opportunities: {missed_opportunities:,} additional projects could have succeeded")
    print(f"• If this category had average success rate, {potential_successful:,} projects would be successful")
else:
    print(f"\n🎯 MAIN ANSWER:")
    print(f"No categories found that are both high-volume (>{median_projects:.0f} projects) and low-success (<{overall_success_rate:.2f}%).")

print(f"\n🔝 TOP 3 MOST DISAPPOINTING CATEGORIES (by disappointment index):")
for i, (category, row) in enumerate(disappointment_ranking.head(3).iterrows(), 1):
    total = int(row['Total_Projects'])
    success_rate = row['Success_Rate']
    index = row['Disappointment_Index']
    print(f"{i}. {category}: {total:,} projects, {success_rate:.2f}% success (Index: {index:.1f})")

print(f"\n💡 INSIGHTS:")
print(f"• Categories with many projects don't always have high success rates")
print(f"• Popular categories may be more competitive, leading to lower success rates")
print(f"• Some categories may have structural challenges that make success difficult")
print(f"• Understanding these patterns can help creators choose better categories or improve their strategies")

print(f"\n📈 METHODOLOGY:")
print(f"• High volume: Categories with > {median_projects:.0f} projects (above median)")
print(f"• Low success: Categories with < {overall_success_rate:.2f}% success rate (below overall average)")
print(f"• Disappointment index: Combines volume rank + inverse success rank")