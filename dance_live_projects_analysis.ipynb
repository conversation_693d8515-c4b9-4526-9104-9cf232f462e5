# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)

# Load the data
df = pd.read_csv('kickstarter_projects (3) (1).csv')

# Display basic information about the dataset
print("Dataset Shape:", df.shape)
print("\nColumn Names:")
print(df.columns.tolist())
print("\nFirst few rows:")
df.head()

# Check all available categories to find Dance
print("All available categories:")
all_categories = sorted(df['Category'].unique())
for i, category in enumerate(all_categories, 1):
    count = len(df[df['Category'] == category])
    print(f"{i:2d}. {category:<20} ({count:,} projects)")

print(f"\nTotal categories: {len(all_categories)}")

# Check if Dance category exists and examine project states
dance_exists = 'Dance' in df['Category'].values
print(f"Dance category exists: {dance_exists}")

if dance_exists:
    dance_projects = df[df['Category'] == 'Dance']
    print(f"\nTotal Dance projects: {len(dance_projects):,}")
    
    print(f"\nProject states in Dance category:")
    dance_states = dance_projects['State'].value_counts()
    for state, count in dance_states.items():
        percentage = (count / len(dance_projects)) * 100
        print(f"{state:<12}: {count:>6,} projects ({percentage:5.2f}%)")
else:
    # Check for similar categories that might contain dance
    dance_related = [cat for cat in all_categories if 'dance' in cat.lower()]
    theater_related = [cat for cat in all_categories if 'theater' in cat.lower() or 'theatre' in cat.lower()]
    music_related = [cat for cat in all_categories if 'music' in cat.lower()]
    
    print(f"\nDance-related categories found: {dance_related}")
    print(f"Theater-related categories found: {theater_related}")
    print(f"Music-related categories found: {music_related}")
    
    # Check subcategories for dance
    print(f"\nChecking subcategories for 'dance'...")
    dance_subcategories = df[df['Subcategory'].str.contains('Dance', case=False, na=False)]
    if len(dance_subcategories) > 0:
        print(f"Found {len(dance_subcategories)} projects with 'Dance' in subcategory:")
        dance_subcat_analysis = dance_subcategories.groupby(['Category', 'Subcategory']).size().reset_index(name='Count')
        for _, row in dance_subcat_analysis.iterrows():
            print(f"  {row['Category']} - {row['Subcategory']}: {row['Count']} projects")
    else:
        print("No subcategories found containing 'Dance'")

# Check all project states in the dataset
print("All project states in the dataset:")
all_states = df['State'].value_counts()
total_projects = len(df)

for state, count in all_states.items():
    percentage = (count / total_projects) * 100
    print(f"{state:<12}: {count:>8,} projects ({percentage:5.2f}%)")

print(f"\nTotal projects: {total_projects:,}")
live_projects_exist = 'Live' in all_states.index
print(f"Live projects exist in dataset: {live_projects_exist}")

# Main analysis: Calculate percentage of live projects in Dance category
if dance_exists:
    dance_projects = df[df['Category'] == 'Dance']
    total_dance_projects = len(dance_projects)
    
    if 'Live' in dance_projects['State'].values:
        live_dance_projects = len(dance_projects[dance_projects['State'] == 'Live'])
        live_percentage = (live_dance_projects / total_dance_projects) * 100
        
        print("🎯 MAIN ANSWER:")
        print("=" * 50)
        print(f"Percentage of Live projects in Dance category: {live_percentage:.2f}%")
        print(f"\n📊 BREAKDOWN:")
        print(f"• Total Dance projects: {total_dance_projects:,}")
        print(f"• Live Dance projects: {live_dance_projects:,}")
        print(f"• Live percentage: {live_percentage:.2f}%")
        
    else:
        print("🎯 MAIN ANSWER:")
        print("=" * 50)
        print(f"Percentage of Live projects in Dance category: 0.00%")
        print(f"\n📊 BREAKDOWN:")
        print(f"• Total Dance projects: {total_dance_projects:,}")
        print(f"• Live Dance projects: 0")
        print(f"• No live projects found in Dance category")
        
else:
    print("🎯 MAIN ANSWER:")
    print("=" * 50)
    print(f"Dance category not found in the dataset.")
    print(f"Cannot calculate percentage of live projects in Dance category.")
    
    # Alternative analysis with dance-related subcategories
    dance_subcategories = df[df['Subcategory'].str.contains('Dance', case=False, na=False)]
    if len(dance_subcategories) > 0:
        total_dance_subcat = len(dance_subcategories)
        if 'Live' in dance_subcategories['State'].values:
            live_dance_subcat = len(dance_subcategories[dance_subcategories['State'] == 'Live'])
            live_subcat_percentage = (live_dance_subcat / total_dance_subcat) * 100
            
            print(f"\n🔍 ALTERNATIVE ANALYSIS (Dance subcategories):")
            print(f"• Total projects with 'Dance' in subcategory: {total_dance_subcat:,}")
            print(f"• Live projects with 'Dance' in subcategory: {live_dance_subcat:,}")
            print(f"• Live percentage: {live_subcat_percentage:.2f}%")
        else:
            print(f"\n🔍 ALTERNATIVE ANALYSIS (Dance subcategories):")
            print(f"• Total projects with 'Dance' in subcategory: {total_dance_subcat:,}")
            print(f"• Live projects with 'Dance' in subcategory: 0")
            print(f"• Live percentage: 0.00%")

# Compare with overall live project percentage
if live_projects_exist:
    total_live_projects = len(df[df['State'] == 'Live'])
    overall_live_percentage = (total_live_projects / total_projects) * 100
    
    print(f"📈 COMPARISON WITH OVERALL DATASET:")
    print("=" * 50)
    print(f"Overall live projects in dataset: {total_live_projects:,} ({overall_live_percentage:.2f}%)")
    
    if dance_exists and 'Live' in df[df['Category'] == 'Dance']['State'].values:
        dance_live_count = len(df[(df['Category'] == 'Dance') & (df['State'] == 'Live')])
        dance_live_pct = (dance_live_count / len(df[df['Category'] == 'Dance'])) * 100
        difference = dance_live_pct - overall_live_percentage
        
        print(f"Dance category live percentage: {dance_live_pct:.2f}%")
        print(f"Difference from overall: {difference:+.2f} percentage points")
        
        if difference > 0:
            print(f"Dance category has a higher proportion of live projects than average.")
        elif difference < 0:
            print(f"Dance category has a lower proportion of live projects than average.")
        else:
            print(f"Dance category has the same proportion of live projects as average.")
    else:
        print(f"Dance category live percentage: 0.00%")
        print(f"Difference from overall: {-overall_live_percentage:.2f} percentage points")
        print(f"Dance category has no live projects while the overall dataset has {overall_live_percentage:.2f}%.")
else:
    print(f"📈 DATASET INFORMATION:")
    print("=" * 50)
    print(f"No live projects found in the entire dataset.")
    print(f"This appears to be a historical dataset with completed projects only.")

# Detailed analysis of Dance category (if it exists)
if dance_exists:
    dance_projects = df[df['Category'] == 'Dance']
    
    print(f"🎭 DETAILED DANCE CATEGORY ANALYSIS:")
    print("=" * 50)
    
    # Subcategories in Dance
    print(f"\nSubcategories in Dance:")
    dance_subcats = dance_projects['Subcategory'].value_counts()
    for subcat, count in dance_subcats.items():
        percentage = (count / len(dance_projects)) * 100
        print(f"  {subcat:<20}: {count:>4} projects ({percentage:5.1f}%)")
    
    # Countries
    print(f"\nTop countries for Dance projects:")
    dance_countries = dance_projects['Country'].value_counts().head(5)
    for country, count in dance_countries.items():
        percentage = (count / len(dance_projects)) * 100
        print(f"  {country:<15}: {count:>4} projects ({percentage:5.1f}%)")
    
    # Success rate
    successful_dance = len(dance_projects[dance_projects['State'] == 'Successful'])
    dance_success_rate = (successful_dance / len(dance_projects)) * 100
    print(f"\nDance category success rate: {dance_success_rate:.2f}%")
    
    # Goal statistics
    print(f"\nGoal statistics for Dance projects:")
    print(f"  Average goal: ${dance_projects['Goal'].mean():,.0f}")
    print(f"  Median goal: ${dance_projects['Goal'].median():,.0f}")
    print(f"  Average pledged: ${dance_projects['Pledged'].mean():,.0f}")
    print(f"  Average backers: {dance_projects['Backers'].mean():.0f}")
    
    # Time analysis
    if 'Launched' in df.columns:
        dance_projects_copy = dance_projects.copy()
        dance_projects_copy['Launched'] = pd.to_datetime(dance_projects_copy['Launched'])
        dance_projects_copy['Year'] = dance_projects_copy['Launched'].dt.year
        
        print(f"\nDance projects by year:")
        yearly_dance = dance_projects_copy['Year'].value_counts().sort_index()
        for year, count in yearly_dance.items():
            print(f"  {year}: {count} projects")
else:
    # Analysis of dance-related subcategories if Dance category doesn't exist
    dance_subcategories = df[df['Subcategory'].str.contains('Dance', case=False, na=False)]
    if len(dance_subcategories) > 0:
        print(f"🎭 DANCE-RELATED PROJECTS ANALYSIS:")
        print("=" * 50)
        
        print(f"\nDance-related subcategories found:")
        dance_subcat_breakdown = dance_subcategories.groupby(['Category', 'Subcategory']).agg({
            'State': ['count', lambda x: (x == 'Successful').sum()]
        })
        dance_subcat_breakdown.columns = ['Total', 'Successful']
        dance_subcat_breakdown['Success_Rate'] = (dance_subcat_breakdown['Successful'] / dance_subcat_breakdown['Total']) * 100
        
        for (category, subcategory), row in dance_subcat_breakdown.iterrows():
            total = int(row['Total'])
            successful = int(row['Successful'])
            success_rate = row['Success_Rate']
            print(f"  {category} - {subcategory}:")
            print(f"    Total: {total}, Successful: {successful}, Success Rate: {success_rate:.1f}%")
        
        # States of dance-related projects
        print(f"\nStates of dance-related projects:")
        dance_related_states = dance_subcategories['State'].value_counts()
        for state, count in dance_related_states.items():
            percentage = (count / len(dance_subcategories)) * 100
            print(f"  {state:<12}: {count:>4} projects ({percentage:5.1f}%)")

# Create visualizations
fig, axes = plt.subplots(2, 2, figsize=(16, 12))

# 1. Overall project states distribution
ax1 = axes[0, 0]
state_counts = df['State'].value_counts()
colors = ['green' if state == 'Live' else 'skyblue' for state in state_counts.index]
wedges, texts, autotexts = ax1.pie(state_counts.values, labels=state_counts.index, 
                                  autopct='%1.1f%%', startangle=90, colors=colors)
ax1.set_title('Overall Project States Distribution', fontsize=14, fontweight='bold')

# Highlight Live projects if they exist
if 'Live' in state_counts.index:
    live_index = list(state_counts.index).index('Live')
    wedges[live_index].set_edgecolor('red')
    wedges[live_index].set_linewidth(3)

# 2. Dance category analysis (if exists)
ax2 = axes[0, 1]
if dance_exists:
    dance_projects = df[df['Category'] == 'Dance']
    dance_states = dance_projects['State'].value_counts()
    colors2 = ['green' if state == 'Live' else 'lightcoral' for state in dance_states.index]
    wedges2, texts2, autotexts2 = ax2.pie(dance_states.values, labels=dance_states.index, 
                                         autopct='%1.1f%%', startangle=90, colors=colors2)
    ax2.set_title('Dance Category Project States', fontsize=14, fontweight='bold')
    
    if 'Live' in dance_states.index:
        live_index2 = list(dance_states.index).index('Live')
        wedges2[live_index2].set_edgecolor('red')
        wedges2[live_index2].set_linewidth(3)
else:
    ax2.text(0.5, 0.5, 'Dance Category\nNot Found', ha='center', va='center', 
             fontsize=16, fontweight='bold', transform=ax2.transAxes)
    ax2.set_xlim(0, 1)
    ax2.set_ylim(0, 1)
    ax2.axis('off')

# 3. Live projects by category (top 10)
ax3 = axes[1, 0]
if live_projects_exist:
    live_by_category = df[df['State'] == 'Live']['Category'].value_counts().head(10)
    bars3 = ax3.bar(range(len(live_by_category)), live_by_category.values, 
                    color='lightgreen', alpha=0.8, edgecolor='darkgreen')
    ax3.set_xticks(range(len(live_by_category)))
    ax3.set_xticklabels(live_by_category.index, rotation=45, ha='right')
    ax3.set_ylabel('Number of Live Projects')
    ax3.set_title('Top 10 Categories by Live Projects', fontsize=14, fontweight='bold')
    
    # Highlight Dance if it exists in top 10
    if 'Dance' in live_by_category.index:
        dance_index = list(live_by_category.index).index('Dance')
        bars3[dance_index].set_color('red')
        bars3[dance_index].set_alpha(1.0)
    
    # Add value labels
    for bar in bars3:
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                 f'{int(height)}', ha='center', va='bottom')
else:
    ax3.text(0.5, 0.5, 'No Live Projects\nFound in Dataset', ha='center', va='center', 
             fontsize=16, fontweight='bold', transform=ax3.transAxes)
    ax3.set_xlim(0, 1)
    ax3.set_ylim(0, 1)
    ax3.axis('off')

# 4. Category comparison (if Dance exists)
ax4 = axes[1, 1]
if dance_exists and live_projects_exist:
    # Compare Dance with overall and other categories
    categories_to_compare = ['Dance']
    
    # Add a few other categories for comparison
    other_categories = df['Category'].value_counts().head(5).index.tolist()
    for cat in other_categories:
        if cat != 'Dance' and len(categories_to_compare) < 6:
            categories_to_compare.append(cat)
    
    live_percentages = []
    for cat in categories_to_compare:
        cat_projects = df[df['Category'] == cat]
        if len(cat_projects) > 0:
            live_count = len(cat_projects[cat_projects['State'] == 'Live'])
            live_pct = (live_count / len(cat_projects)) * 100
            live_percentages.append(live_pct)
        else:
            live_percentages.append(0)
    
    colors4 = ['red' if cat == 'Dance' else 'lightblue' for cat in categories_to_compare]
    bars4 = ax4.bar(range(len(categories_to_compare)), live_percentages, 
                    color=colors4, alpha=0.8, edgecolor='navy')
    ax4.set_xticks(range(len(categories_to_compare)))
    ax4.set_xticklabels(categories_to_compare, rotation=45, ha='right')
    ax4.set_ylabel('Live Projects Percentage (%)')
    ax4.set_title('Live Projects Percentage by Category', fontsize=14, fontweight='bold')
    
    # Add value labels
    for bar, pct in zip(bars4, live_percentages):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                 f'{pct:.1f}%', ha='center', va='bottom')
else:
    ax4.text(0.5, 0.5, 'Cannot Create\nComparison Chart', ha='center', va='center', 
             fontsize=16, fontweight='bold', transform=ax4.transAxes)
    ax4.set_xlim(0, 1)
    ax4.set_ylim(0, 1)
    ax4.axis('off')

plt.tight_layout()
plt.show()

# Final summary and conclusion
print("=" * 80)
print("🎯 FINAL SUMMARY: PERCENTAGE OF LIVE PROJECTS IN DANCE CATEGORY")
print("=" * 80)

if dance_exists:
    dance_projects = df[df['Category'] == 'Dance']
    total_dance = len(dance_projects)
    
    if 'Live' in dance_projects['State'].values:
        live_dance = len(dance_projects[dance_projects['State'] == 'Live'])
        live_percentage = (live_dance / total_dance) * 100
        
        print(f"\n🎭 MAIN ANSWER:")
        print(f"Percentage of Live projects in Dance category: {live_percentage:.2f}%")
        
        print(f"\n📊 DETAILED BREAKDOWN:")
        print(f"• Total Dance projects: {total_dance:,}")
        print(f"• Live Dance projects: {live_dance:,}")
        print(f"• Live percentage: {live_percentage:.2f}%")
        
        # Context
        if live_projects_exist:
            overall_live = len(df[df['State'] == 'Live'])
            overall_live_pct = (overall_live / len(df)) * 100
            print(f"\n📈 CONTEXT:")
            print(f"• Overall live percentage in dataset: {overall_live_pct:.2f}%")
            print(f"• Dance vs overall difference: {live_percentage - overall_live_pct:+.2f} percentage points")
    else:
        print(f"\n🎭 MAIN ANSWER:")
        print(f"Percentage of Live projects in Dance category: 0.00%")
        
        print(f"\n📊 DETAILED BREAKDOWN:")
        print(f"• Total Dance projects: {total_dance:,}")
        print(f"• Live Dance projects: 0")
        print(f"• No live projects found in Dance category")
        
        print(f"\n📋 DANCE CATEGORY STATES:")
        dance_states = dance_projects['State'].value_counts()
        for state, count in dance_states.items():
            percentage = (count / total_dance) * 100
            print(f"• {state}: {count:,} projects ({percentage:.2f}%)")
            
else:
    print(f"\n🎭 MAIN ANSWER:")
    print(f"Dance category not found in the dataset.")
    print(f"Cannot calculate percentage of live projects in Dance category.")
    
    # Check for dance-related projects
    dance_subcategories = df[df['Subcategory'].str.contains('Dance', case=False, na=False)]
    if len(dance_subcategories) > 0:
        total_dance_related = len(dance_subcategories)
        live_dance_related = len(dance_subcategories[dance_subcategories['State'] == 'Live']) if 'Live' in dance_subcategories['State'].values else 0
        live_related_pct = (live_dance_related / total_dance_related) * 100 if total_dance_related > 0 else 0
        
        print(f"\n🔍 ALTERNATIVE ANALYSIS (Dance-related subcategories):")
        print(f"• Projects with 'Dance' in subcategory: {total_dance_related:,}")
        print(f"• Live dance-related projects: {live_dance_related:,}")
        print(f"• Live percentage: {live_related_pct:.2f}%")
        
        print(f"\n📋 DANCE-RELATED PROJECT CATEGORIES:")
        dance_categories = dance_subcategories['Category'].value_counts()
        for category, count in dance_categories.items():
            print(f"• {category}: {count} projects")

# Dataset insights
print(f"\n💡 DATASET INSIGHTS:")
if live_projects_exist:
    total_live = len(df[df['State'] == 'Live'])
    overall_live_pct = (total_live / len(df)) * 100
    print(f"• Dataset contains {total_live:,} live projects ({overall_live_pct:.2f}% of all projects)")
    print(f"• This suggests the dataset includes ongoing campaigns")
else:
    print(f"• Dataset contains no live projects")
    print(f"• This appears to be a historical dataset with completed projects only")
    print(f"• All projects have reached their final state (Successful, Failed, Canceled, etc.)")

print(f"\n📊 METHODOLOGY:")
print(f"• Searched for 'Dance' as a main category")
print(f"• Calculated percentage as: (Live Dance projects / Total Dance projects) × 100")
print(f"• Also checked for dance-related subcategories as alternative analysis")
print(f"• Compared with overall dataset live project percentage for context")

print(f"\n📈 SUMMARY STATISTICS:")
print(f"• Total projects in dataset: {len(df):,}")
print(f"• Total categories: {df['Category'].nunique()}")
print(f"• Total project states: {df['State'].nunique()}")
if dance_exists:
    print(f"• Dance category rank by project count: {list(df['Category'].value_counts().index).index('Dance') + 1}")
    dance_share = (len(df[df['Category'] == 'Dance']) / len(df)) * 100
    print(f"• Dance category share of all projects: {dance_share:.3f}%")