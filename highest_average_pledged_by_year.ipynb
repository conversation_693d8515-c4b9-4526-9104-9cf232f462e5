{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Analysis: Which Year Had the Highest Average Pledged Amount\n", "\n", "This notebook analyzes Kickstarter projects to determine which year (based on launch date) had the highest average pledged amount, with detailed insights and trends."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (12, 8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the data\n", "df = pd.read_csv('kickstarter_projects (3) (1).csv')\n", "\n", "# Display basic information about the dataset\n", "print(\"Dataset Shape:\", df.shape)\n", "print(\"\\nColumn Names:\")\n", "print(df.columns.tolist())\n", "print(\"\\nFirst few rows:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check and process the date and pledged columns\n", "print(\"Data types and basic info:\")\n", "print(f\"Launched column type: {df['Launched'].dtype}\")\n", "print(f\"Pledged column type: {df['Pledged'].dtype}\")\n", "print(f\"\\nMissing values:\")\n", "print(f\"Launched: {df['Launched'].isnull().sum()}\")\n", "print(f\"Pledged: {df['Pledged'].isnull().sum()}\")\n", "\n", "# Convert Launched to datetime\n", "df['Launched'] = pd.to_datetime(df['Launched'])\n", "df['Launch_Year'] = df['Launched'].dt.year\n", "\n", "print(f\"\\nDate range in dataset:\")\n", "print(f\"From: {df['Launched'].min()}\")\n", "print(f\"To: {df['Launched'].max()}\")\n", "print(f\"Years covered: {df['Launch_Year'].min()} - {df['Launch_Year'].max()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic statistics for pledged amounts\n", "print(\"Pledged Amount Statistics:\")\n", "print(df['Pledged'].describe())\n", "\n", "# Check for any unusual values\n", "print(f\"\\nPledged amount analysis:\")\n", "print(f\"Zero pledged projects: {len(df[df['Pledged'] == 0]):,}\")\n", "print(f\"Projects with pledged > $1M: {len(df[df['Pledged'] > 1000000]):,}\")\n", "print(f\"Projects with pledged > $10M: {len(df[df['Pledged'] > 10000000]):,}\")\n", "\n", "# Remove any rows with missing critical data\n", "initial_count = len(df)\n", "df = df.dropna(subset=['Launched', 'Pledged'])\n", "final_count = len(df)\n", "print(f\"\\nDataset after removing missing values: {final_count:,} projects (removed {initial_count - final_count:,})\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate average pledged amount by year\n", "yearly_pledged = df.groupby('Launch_Year').agg({\n", "    'Pledged': ['count', 'sum', 'mean', 'median', 'std']\n", "}).round(2)\n", "\n", "yearly_pledged.columns = ['Project_Count', 'Total_Pledged', 'Average_Pledged', 'Median_Pledged', 'Std_Pledged']\n", "yearly_pledged = yearly_pledged.sort_values('Average_Pledged', ascending=False)\n", "\n", "print(\"📊 AVERAGE PLEDGED AMOUNT BY YEAR (sorted by average):\")\n", "print(\"=\" * 80)\n", "print(f\"{'Year':<6} {'Projects':<8} {'Total Pledged':<15} {'Average':<12} {'Median':<10} {'Std Dev':<12}\")\n", "print(\"-\" * 80)\n", "\n", "for year, row in yearly_pledged.iterrows():\n", "    projects = int(row['Project_Count'])\n", "    total = row['Total_Pledged']\n", "    average = row['Average_Pledged']\n", "    median = row['Median_Pledged']\n", "    std = row['Std_Pledged']\n", "    print(f\"{year:<6} {projects:<8,} ${total:<14,.0f} ${average:<11,.0f} ${median:<9,.0f} ${std:<11,.0f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Find the year with highest average pledged\n", "best_year = yearly_pledged.index[0]\n", "best_average = yearly_pledged.iloc[0]['Average_Pledged']\n", "best_stats = yearly_pledged.iloc[0]\n", "\n", "print(f\"🎯 ANSWER: Year with highest average pledged amount:\")\n", "print(f\"{best_year} with an average of ${best_average:,.2f} per project\")\n", "\n", "print(f\"\\n📊 DETAILED STATISTICS FOR {best_year}:\")\n", "print(f\"• Number of projects: {int(best_stats['Project_Count']):,}\")\n", "print(f\"• Total amount pledged: ${best_stats['Total_Pledged']:,.2f}\")\n", "print(f\"• Average pledged: ${best_stats['Average_Pledged']:,.2f}\")\n", "print(f\"• Median pledged: ${best_stats['Median_Pledged']:,.2f}\")\n", "print(f\"• Standard deviation: ${best_stats['Std_Pledged']:,.2f}\")\n", "\n", "# Calculate overall average for comparison\n", "overall_average = df['Pledged'].mean()\n", "print(f\"\\n📈 COMPARISON:\")\n", "print(f\"• Overall dataset average: ${overall_average:,.2f}\")\n", "print(f\"• {best_year} vs overall: {((best_average / overall_average) - 1) * 100:+.1f}% difference\")\n", "\n", "# Show top 5 years\n", "print(f\"\\n🏆 TOP 5 YEARS BY AVERAGE PLEDGED:\")\n", "for i, (year, row) in enumerate(yearly_pledged.head(5).iterrows(), 1):\n", "    avg = row['Average_Pledged']\n", "    projects = int(row['Project_Count'])\n", "    print(f\"{i}. {year}: ${avg:,.0f} average ({projects:,} projects)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze what made the best year special\n", "best_year_data = df[df['Launch_Year'] == best_year]\n", "\n", "print(f\"🔍 ANALYSIS OF {best_year} - THE BEST PERFORMING YEAR:\")\n", "print(\"=\" * 60)\n", "\n", "# Category analysis for best year\n", "print(f\"\\nTop categories in {best_year} by average pledged:\")\n", "best_year_categories = best_year_data.groupby('Category').agg({\n", "    'Pledged': ['count', 'mean']\n", "})\n", "best_year_categories.columns = ['Count', 'Average_Pledged']\n", "best_year_categories = best_year_categories[best_year_categories['Count'] >= 5]  # Min 5 projects\n", "best_year_categories = best_year_categories.sort_values('Average_Pledged', ascending=False)\n", "\n", "for i, (category, row) in enumerate(best_year_categories.head(10).iterrows(), 1):\n", "    count = int(row['Count'])\n", "    avg = row['Average_Pledged']\n", "    print(f\"{i:2d}. {category:<15}: ${avg:>10,.0f} average ({count} projects)\")\n", "\n", "# Success rate analysis\n", "best_year_success_rate = (len(best_year_data[best_year_data['State'] == 'Successful']) / len(best_year_data)) * 100\n", "overall_success_rate = (len(df[df['State'] == 'Successful']) / len(df)) * 100\n", "\n", "print(f\"\\nSuccess rate analysis for {best_year}:\")\n", "print(f\"• {best_year} success rate: {best_year_success_rate:.2f}%\")\n", "print(f\"• Overall success rate: {overall_success_rate:.2f}%\")\n", "print(f\"• Difference: {best_year_success_rate - overall_success_rate:+.2f} percentage points\")\n", "\n", "# Goal vs pledged analysis\n", "best_year_avg_goal = best_year_data['Goal'].mean()\n", "overall_avg_goal = df['Goal'].mean()\n", "\n", "print(f\"\\nGoal amount analysis for {best_year}:\")\n", "print(f\"• {best_year} average goal: ${best_year_avg_goal:,.0f}\")\n", "print(f\"• Overall average goal: ${overall_avg_goal:,.0f}\")\n", "print(f\"• Goal difference: {((best_year_avg_goal / overall_avg_goal) - 1) * 100:+.1f}%\")\n", "\n", "# High-value projects in best year\n", "high_value_threshold = 100000  # $100K+\n", "best_year_high_value = len(best_year_data[best_year_data['Pledged'] >= high_value_threshold])\n", "best_year_high_value_pct = (best_year_high_value / len(best_year_data)) * 100\n", "\n", "overall_high_value = len(df[df['Pledged'] >= high_value_threshold])\n", "overall_high_value_pct = (overall_high_value / len(df)) * 100\n", "\n", "print(f\"\\nHigh-value projects (${high_value_threshold:,}+) in {best_year}:\")\n", "print(f\"• {best_year}: {best_year_high_value} projects ({best_year_high_value_pct:.2f}%)\")\n", "print(f\"• Overall: {overall_high_value} projects ({overall_high_value_pct:.2f}%)\")\n", "print(f\"• {best_year} had {best_year_high_value_pct / overall_high_value_pct:.1f}x the rate of high-value projects\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze trends over time\n", "yearly_pledged_chronological = yearly_pledged.sort_index()\n", "\n", "print(f\"📈 TRENDS ANALYSIS:\")\n", "print(\"=\" * 50)\n", "\n", "# Calculate year-over-year growth\n", "yearly_pledged_chronological['YoY_Growth'] = yearly_pledged_chronological['Average_Pledged'].pct_change() * 100\n", "\n", "print(f\"\\nYear-over-year growth in average pledged:\")\n", "for year, row in yearly_pledged_chronological.iterrows():\n", "    avg = row['Average_Pledged']\n", "    growth = row['YoY_Growth']\n", "    if pd.notna(growth):\n", "        print(f\"{year}: ${avg:>8,.0f} ({growth:+6.1f}% vs previous year)\")\n", "    else:\n", "        print(f\"{year}: ${avg:>8,.0f} (baseline year)\")\n", "\n", "# Find best and worst growth years\n", "growth_data = yearly_pledged_chronological.dropna(subset=['YoY_Growth'])\n", "if len(growth_data) > 0:\n", "    best_growth_year = growth_data['YoY_Growth'].idxmax()\n", "    best_growth_rate = growth_data.loc[best_growth_year, 'YoY_Growth']\n", "    worst_growth_year = growth_data['YoY_Growth'].idxmin()\n", "    worst_growth_rate = growth_data.loc[worst_growth_year, 'YoY_Growth']\n", "    \n", "    print(f\"\\n📊 GROWTH INSIGHTS:\")\n", "    print(f\"• Best growth year: {best_growth_year} (+{best_growth_rate:.1f}%)\")\n", "    print(f\"• Worst growth year: {worst_growth_year} ({worst_growth_rate:.1f}%)\")\n", "\n", "# Calculate correlation between project count and average pledged\n", "correlation = yearly_pledged_chronological['Project_Count'].corr(yearly_pledged_chronological['Average_Pledged'])\n", "print(f\"\\n🔗 CORRELATION ANALYSIS:\")\n", "print(f\"• Correlation between project count and average pledged: {correlation:.3f}\")\n", "if correlation > 0.3:\n", "    print(f\"• Positive correlation: More projects tend to coincide with higher average pledged\")\n", "elif correlation < -0.3:\n", "    print(f\"• Negative correlation: More projects tend to coincide with lower average pledged\")\n", "else:\n", "    print(f\"• Weak correlation: Project count and average pledged are not strongly related\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive visualizations\n", "fig, axes = plt.subplots(2, 2, figsize=(20, 16))\n", "\n", "# 1. Average pledged by year (line chart)\n", "ax1 = axes[0, 0]\n", "years = yearly_pledged_chronological.index\n", "averages = yearly_pledged_chronological['Average_Pledged']\n", "\n", "ax1.plot(years, averages, marker='o', linewidth=3, markersize=8, color='blue')\n", "ax1.scatter(best_year, best_average, color='red', s=200, zorder=5, label=f'Highest: {best_year}')\n", "ax1.set_xlabel('Year')\n", "ax1.set_ylabel('Average Pledged Amount ($)')\n", "ax1.set_title('Average Pledged Amount by Year', fontsize=14, fontweight='bold')\n", "ax1.grid(True, alpha=0.3)\n", "ax1.legend()\n", "\n", "# Format y-axis to show currency\n", "ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))\n", "\n", "# Annotate the best year\n", "ax1.annotate(f'${best_average:,.0f}', \n", "             xy=(best_year, best_average), xytext=(10, 10),\n", "             textcoords='offset points', fontsize=12, fontweight='bold',\n", "             bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),\n", "             arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))\n", "\n", "# 2. Project count by year (bar chart)\n", "ax2 = axes[0, 1]\n", "project_counts = yearly_pledged_chronological['Project_Count']\n", "colors = ['red' if year == best_year else 'skyblue' for year in years]\n", "\n", "bars2 = ax2.bar(years, project_counts, color=colors, alpha=0.8, edgecolor='navy')\n", "ax2.set_xlabel('Year')\n", "ax2.set_ylabel('Number of Projects')\n", "ax2.set_title('Number of Projects by Year', fontsize=14, fontweight='bold')\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# Add value labels on bars\n", "for bar, count in zip(bars2, project_counts):\n", "    height = bar.get_height()\n", "    ax2.text(bar.get_x() + bar.get_width()/2., height + max(project_counts)*0.01,\n", "             f'{int(count):,}', ha='center', va='bottom', fontsize=9, rotation=90)\n", "\n", "# 3. Total pledged by year (bar chart)\n", "ax3 = axes[1, 0]\n", "total_pledged = yearly_pledged_chronological['Total_Pledged']\n", "colors3 = ['red' if year == best_year else 'lightgreen' for year in years]\n", "\n", "bars3 = ax3.bar(years, total_pledged, color=colors3, alpha=0.8, edgecolor='darkgreen')\n", "ax3.set_xlabel('Year')\n", "ax3.set_ylabel('Total Pledged Amount ($)')\n", "ax3.set_title('Total Pledged Amount by Year', fontsize=14, fontweight='bold')\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# Format y-axis\n", "ax3.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1e6:.1f}M'))\n", "\n", "# 4. Average vs Median pledged comparison\n", "ax4 = axes[1, 1]\n", "median_pledged = yearly_pledged_chronological['Median_Pledged']\n", "\n", "ax4.plot(years, averages, marker='o', linewidth=2, label='Average', color='blue')\n", "ax4.plot(years, median_pledged, marker='s', linewidth=2, label='Median', color='orange')\n", "ax4.set_xlabel('Year')\n", "ax4.set_ylabel('Pledged Amount ($)')\n", "ax4.set_title('Average vs Median Pledged by Year', fontsize=14, fontweight='bold')\n", "ax4.grid(True, alpha=0.3)\n", "ax4.legend()\n", "\n", "# Format y-axis\n", "ax4.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze the most successful projects in the best year\n", "best_year_data = df[df['Launch_Year'] == best_year]\n", "top_projects_best_year = best_year_data.nlargest(10, 'Pledged')\n", "\n", "print(f\"🌟 TOP 10 HIGHEST PLEDGED PROJECTS IN {best_year}:\")\n", "print(\"=\" * 80)\n", "print(f\"{'Rank':<4} {'Project Name':<35} {'Category':<15} {'Pledged':<12} {'Goal':<10}\")\n", "print(\"-\" * 80)\n", "\n", "for i, (_, project) in enumerate(top_projects_best_year.iterrows(), 1):\n", "    name = project['Name'][:32] + \"...\" if len(project['Name']) > 32 else project['Name']\n", "    category = project['Category']\n", "    pledged = project['Pledged']\n", "    goal = project['Goal']\n", "    print(f\"{i:<4} {name:<35} {category:<15} ${pledged:<11,.0f} ${goal:<9,.0f}\")\n", "\n", "# Calculate what percentage these top projects contributed\n", "top_10_total = top_projects_best_year['Pledged'].sum()\n", "best_year_total = best_year_data['Pledged'].sum()\n", "top_10_percentage = (top_10_total / best_year_total) * 100\n", "\n", "print(f\"\\n📊 TOP 10 IMPACT IN {best_year}:\")\n", "print(f\"• Top 10 projects total: ${top_10_total:,.0f}\")\n", "print(f\"• {best_year} total pledged: ${best_year_total:,.0f}\")\n", "print(f\"• Top 10 contribution: {top_10_percentage:.1f}% of total pledged amount\")\n", "print(f\"• Average of top 10: ${top_10_total/10:,.0f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare best year with worst year\n", "worst_year = yearly_pledged.index[-1]\n", "worst_average = yearly_pledged.iloc[-1]['Average_Pledged']\n", "worst_stats = yearly_pledged.iloc[-1]\n", "\n", "print(f\"🔍 BEST vs WORST YEAR COMPARISON:\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\nBEST YEAR - {best_year}:\")\n", "print(f\"• Average pledged: ${best_average:,.0f}\")\n", "print(f\"• Number of projects: {int(best_stats['Project_Count']):,}\")\n", "print(f\"• Total pledged: ${best_stats['Total_Pledged']:,.0f}\")\n", "print(f\"• Median pledged: ${best_stats['Median_Pledged']:,.0f}\")\n", "\n", "print(f\"\\nWORST YEAR - {worst_year}:\")\n", "print(f\"• Average pledged: ${worst_average:,.0f}\")\n", "print(f\"• Number of projects: {int(worst_stats['Project_Count']):,}\")\n", "print(f\"• Total pledged: ${worst_stats['Total_Pledged']:,.0f}\")\n", "print(f\"• Median pledged: ${worst_stats['Median_Pledged']:,.0f}\")\n", "\n", "print(f\"\\n📈 DIFFERENCES:\")\n", "avg_ratio = best_average / worst_average\n", "project_ratio = best_stats['Project_Count'] / worst_stats['Project_Count']\n", "total_ratio = best_stats['Total_Pledged'] / worst_stats['Total_Pledged']\n", "\n", "print(f\"• Average pledged ratio: {avg_ratio:.1f}x ({best_year} vs {worst_year})\")\n", "print(f\"• Project count ratio: {project_ratio:.1f}x\")\n", "print(f\"• Total pledged ratio: {total_ratio:.1f}x\")\n", "print(f\"• Average difference: ${best_average - worst_average:,.0f}\")\n", "\n", "# Analyze what categories drove the difference\n", "best_year_data = df[df['Launch_Year'] == best_year]\n", "worst_year_data = df[df['Launch_Year'] == worst_year]\n", "\n", "print(f\"\\n🏷️ CATEGORY ANALYSIS:\")\n", "print(f\"Categories in {best_year}: {best_year_data['Category'].nunique()}\")\n", "print(f\"Categories in {worst_year}: {worst_year_data['Category'].nunique()}\")\n", "\n", "# Top category in each year\n", "best_year_top_cat = best_year_data.groupby('Category')['Pledged'].mean().idxmax()\n", "best_year_top_avg = best_year_data.groupby('Category')['Pledged'].mean().max()\n", "\n", "worst_year_top_cat = worst_year_data.groupby('Category')['Pledged'].mean().idxmax()\n", "worst_year_top_avg = worst_year_data.groupby('Category')['Pledged'].mean().max()\n", "\n", "print(f\"\\nTop category by average pledged:\")\n", "print(f\"• {best_year}: {best_year_top_cat} (${best_year_top_avg:,.0f} average)\")\n", "print(f\"• {worst_year}: {worst_year_top_cat} (${worst_year_top_avg:,.0f} average)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Statistical analysis and insights\n", "print(f\"📊 STATISTICAL INSIGHTS:\")\n", "print(\"=\" * 50)\n", "\n", "# Calculate coefficient of variation for each year\n", "yearly_pledged_chronological['CV'] = (yearly_pledged_chronological['Std_Pledged'] / yearly_pledged_chronological['Average_Pledged']) * 100\n", "\n", "print(f\"\\nVariability analysis (Coefficient of Variation):\")\n", "most_variable_year = yearly_pledged_chronological['CV'].idxmax()\n", "least_variable_year = yearly_pledged_chronological['CV'].idxmin()\n", "\n", "print(f\"• Most variable year: {most_variable_year} (CV: {yearly_pledged_chronological.loc[most_variable_year, 'CV']:.1f}%)\")\n", "print(f\"• Least variable year: {least_variable_year} (CV: {yearly_pledged_chronological.loc[least_variable_year, 'CV']:.1f}%)\")\n", "print(f\"• {best_year} variability: {yearly_pledged_chronological.loc[best_year, 'CV']:.1f}%\")\n", "\n", "if yearly_pledged_chronological.loc[best_year, 'CV'] > yearly_pledged_chronological['CV'].median():\n", "    print(f\"• {best_year} had higher than average variability (more extreme successes and failures)\")\n", "else:\n", "    print(f\"• {best_year} had lower than average variability (more consistent pledged amounts)\")\n", "\n", "# Trend analysis\n", "print(f\"\\n📈 TREND ANALYSIS:\")\n", "early_years = yearly_pledged_chronological.head(3)['Average_Pledged'].mean()\n", "recent_years = yearly_pledged_chronological.tail(3)['Average_Pledged'].mean()\n", "trend_direction = \"increasing\" if recent_years > early_years else \"decreasing\"\n", "trend_magnitude = abs((recent_years / early_years - 1) * 100)\n", "\n", "print(f\"• Overall trend: {trend_direction} by {trend_magnitude:.1f}%\")\n", "print(f\"• Early years average (first 3): ${early_years:,.0f}\")\n", "print(f\"• Recent years average (last 3): ${recent_years:,.0f}\")\n", "\n", "# Peak analysis\n", "years_above_overall = yearly_pledged_chronological[yearly_pledged_chronological['Average_Pledged'] > overall_average]\n", "print(f\"\\n🏔️ PEAK PERFORMANCE:\")\n", "print(f\"• Years above overall average: {len(years_above_overall)} out of {len(yearly_pledged_chronological)}\")\n", "print(f\"• Peak years: {', '.join(map(str, years_above_overall.index.tolist()))}\")\n", "print(f\"• {best_year} was {((best_average / overall_average) - 1) * 100:.1f}% above the overall average\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final summary and conclusions\n", "print(\"=\" * 80)\n", "print(\"🎯 FINAL SUMMARY: YEAR WITH HIGHEST AVERAGE PLEDGED AMOUNT\")\n", "print(\"=\" * 80)\n", "\n", "print(f\"\\n🏆 MAIN ANSWER:\")\n", "print(f\"{best_year} had the highest average pledged amount at ${best_average:,.2f} per project\")\n", "\n", "print(f\"\\n📊 KEY STATISTICS FOR {best_year}:\")\n", "print(f\"• Total projects launched: {int(best_stats['Project_Count']):,}\")\n", "print(f\"• Total amount pledged: ${best_stats['Total_Pledged']:,.0f}\")\n", "print(f\"• Average pledged per project: ${best_stats['Average_Pledged']:,.0f}\")\n", "print(f\"• Median pledged per project: ${best_stats['Median_Pledged']:,.0f}\")\n", "print(f\"• Standard deviation: ${best_stats['Std_Pledged']:,.0f}\")\n", "\n", "print(f\"\\n📈 PERFORMANCE COMPARISON:\")\n", "print(f\"• vs Overall average: {((best_average / overall_average) - 1) * 100:+.1f}%\")\n", "print(f\"• vs Worst year ({worst_year}): {((best_average / worst_average) - 1) * 100:+.1f}%\")\n", "print(f\"• Rank among all years: #1 out of {len(yearly_pledged)}\")\n", "\n", "print(f\"\\n🔝 TOP 5 YEARS BY AVERAGE PLEDGED:\")\n", "for i, (year, row) in enumerate(yearly_pledged.head(5).iterrows(), 1):\n", "    avg = row['Average_Pledged']\n", "    projects = int(row['Project_Count'])\n", "    total = row['Total_Pledged']\n", "    print(f\"{i}. {year}: ${avg:>8,.0f} average | {projects:>6,} projects | ${total:>12,.0f} total\")\n", "\n", "print(f\"\\n💡 KEY INSIGHTS:\")\n", "print(f\"• {best_year} achieved the highest average through a combination of factors:\")\n", "\n", "# Determine what made the year special\n", "best_year_data = df[df['Launch_Year'] == best_year]\n", "high_value_projects = len(best_year_data[best_year_data['Pledged'] >= 100000])\n", "success_rate = (len(best_year_data[best_year_data['State'] == 'Successful']) / len(best_year_data)) * 100\n", "\n", "print(f\"  - {high_value_projects} projects raised $100K+ ({(high_value_projects/len(best_year_data)*100):.1f}% of projects)\")\n", "print(f\"  - Success rate of {success_rate:.1f}% (vs {overall_success_rate:.1f}% overall)\")\n", "print(f\"  - Strong performance across multiple categories\")\n", "\n", "if best_stats['Project_Count'] > yearly_pledged['Project_Count'].median():\n", "    print(f\"  - High project volume ({int(best_stats['Project_Count']):,} projects)\")\n", "else:\n", "    print(f\"  - Quality over quantity ({int(best_stats['Project_Count']):,} projects, below median)\")\n", "\n", "print(f\"\\n📊 METHODOLOGY:\")\n", "print(f\"• Analysis based on launch date (Launched column)\")\n", "print(f\"• Average calculated as total pledged ÷ number of projects per year\")\n", "print(f\"• Includes all project states (successful, failed, canceled, etc.)\")\n", "print(f\"• Dataset covers {df['Launch_Year'].min()}-{df['Launch_Year'].max()} ({len(yearly_pledged)} years)\")\n", "print(f\"• Total projects analyzed: {len(df):,}\")\n", "\n", "print(f\"\\n🎯 CONCLUSION:\")\n", "print(f\"{best_year} stands out as the peak year for Kickstarter project funding,\")\n", "print(f\"with an average pledged amount of ${best_average:,.0f} per project,\")\n", "print(f\"representing a {((best_average / overall_average) - 1) * 100:.1f}% premium over the overall dataset average.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}