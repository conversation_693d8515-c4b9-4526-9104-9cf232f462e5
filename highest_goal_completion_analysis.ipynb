# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)

# Load the data
df = pd.read_csv('kickstarter_projects (3) (1).csv')

# Display basic information about the dataset
print("Dataset Shape:", df.shape)
print("\nColumn Names:")
print(df.columns.tolist())
print("\nFirst few rows:")
df.head()

# Check data types and basic statistics for Goal and Pledged columns
print("Data Types:")
print(df[['Goal', 'Pledged', 'State']].dtypes)
print("\nBasic Statistics for Goal and Pledged:")
print(df[['Goal', 'Pledged']].describe())
print("\nMissing values:")
print(df[['Goal', 'Pledged', 'State']].isnull().sum())

# Filter projects with goal over $5000
high_goal_projects = df[df['Goal'] > 5000].copy()

print(f"Total projects in dataset: {len(df):,}")
print(f"Projects with goal > $5000: {len(high_goal_projects):,}")
print(f"Percentage of projects with goal > $5000: {len(high_goal_projects)/len(df)*100:.2f}%")

# Check the distribution of states for high-goal projects
print("\nState distribution for projects with goal > $5000:")
print(high_goal_projects['State'].value_counts())

# Calculate the pledged-to-goal ratio (goal completion percentage)
high_goal_projects['Goal_Completion_Ratio'] = high_goal_projects['Pledged'] / high_goal_projects['Goal']
high_goal_projects['Goal_Completion_Percentage'] = high_goal_projects['Goal_Completion_Ratio'] * 100

# Sort by goal completion ratio in descending order
top_performers = high_goal_projects.sort_values('Goal_Completion_Ratio', ascending=False)

print("Basic statistics for Goal Completion Ratio:")
print(high_goal_projects['Goal_Completion_Ratio'].describe())
print(f"\nProjects that exceeded their goal (ratio > 1): {len(high_goal_projects[high_goal_projects['Goal_Completion_Ratio'] > 1]):,}")
print(f"Projects that met exactly their goal (ratio = 1): {len(high_goal_projects[high_goal_projects['Goal_Completion_Ratio'] == 1]):,}")
print(f"Projects that didn't meet their goal (ratio < 1): {len(high_goal_projects[high_goal_projects['Goal_Completion_Ratio'] < 1]):,}")

# Find the project with the highest goal completion ratio
top_project = top_performers.iloc[0]

print("🏆 PROJECT WITH HIGHEST GOAL COMPLETION RATIO (Goal > $5000):")
print("=" * 70)
print(f"Project Name: {top_project['Name']}")
print(f"Category: {top_project['Category']}")
print(f"Subcategory: {top_project['Subcategory']}")
print(f"Country: {top_project['Country']}")
print(f"State: {top_project['State']}")
print(f"\n💰 FINANCIAL DETAILS:")
print(f"Goal: ${top_project['Goal']:,.2f}")
print(f"Pledged: ${top_project['Pledged']:,.2f}")
print(f"Goal Completion Ratio: {top_project['Goal_Completion_Ratio']:.2f}")
print(f"Goal Completion Percentage: {top_project['Goal_Completion_Percentage']:.2f}%")
print(f"Amount Over Goal: ${top_project['Pledged'] - top_project['Goal']:,.2f}")
print(f"Number of Backers: {top_project['Backers']:,}")
print(f"\n📅 DATES:")
print(f"Launched: {top_project['Launched']}")
print(f"Deadline: {top_project['Deadline']}")

# Show top 20 projects with highest goal completion ratios
print("🔝 TOP 20 PROJECTS WITH HIGHEST GOAL COMPLETION RATIOS (Goal > $5000):")
print("=" * 100)

top_20 = top_performers.head(20)
for i, (idx, project) in enumerate(top_20.iterrows(), 1):
    ratio = project['Goal_Completion_Ratio']
    percentage = project['Goal_Completion_Percentage']
    goal = project['Goal']
    pledged = project['Pledged']
    name = project['Name'][:50] + "..." if len(project['Name']) > 50 else project['Name']
    
    print(f"{i:2d}. {name}")
    print(f"    Goal: ${goal:,.0f} | Pledged: ${pledged:,.0f} | Ratio: {ratio:.2f} ({percentage:.1f}%)")
    print(f"    Category: {project['Category']} - {project['Subcategory']} | State: {project['State']}")
    print()

# Analyze successful projects (those that exceeded their goal)
successful_projects = high_goal_projects[high_goal_projects['Goal_Completion_Ratio'] > 1]

print(f"📊 ANALYSIS OF SUCCESSFUL PROJECTS (Goal > $5000 AND Ratio > 1):")
print("=" * 60)
print(f"Number of successful projects: {len(successful_projects):,}")
print(f"Percentage of high-goal projects that succeeded: {len(successful_projects)/len(high_goal_projects)*100:.2f}%")
print(f"\nGoal Completion Statistics for Successful Projects:")
print(f"Mean ratio: {successful_projects['Goal_Completion_Ratio'].mean():.2f}")
print(f"Median ratio: {successful_projects['Goal_Completion_Ratio'].median():.2f}")
print(f"Standard deviation: {successful_projects['Goal_Completion_Ratio'].std():.2f}")
print(f"Maximum ratio: {successful_projects['Goal_Completion_Ratio'].max():.2f}")
print(f"Minimum ratio: {successful_projects['Goal_Completion_Ratio'].min():.2f}")

# Category analysis for successful projects
print(f"\n🏷️ Top categories for successful high-goal projects:")
category_success = successful_projects['Category'].value_counts().head(10)
for category, count in category_success.items():
    percentage = (count / len(successful_projects)) * 100
    print(f"{category}: {count} projects ({percentage:.1f}%)")

# Create visualizations
fig, axes = plt.subplots(2, 2, figsize=(20, 16))

# 1. Distribution of goal completion ratios
ax1 = axes[0, 0]
# Use log scale for better visualization due to extreme outliers
ratios_for_hist = high_goal_projects['Goal_Completion_Ratio']
ratios_for_hist = ratios_for_hist[ratios_for_hist <= 10]  # Cap at 10 for better visualization
ax1.hist(ratios_for_hist, bins=50, color='skyblue', alpha=0.7, edgecolor='black')
ax1.axvline(x=1, color='red', linestyle='--', linewidth=2, label='Goal Achievement (Ratio = 1)')
ax1.set_xlabel('Goal Completion Ratio')
ax1.set_ylabel('Number of Projects')
ax1.set_title('Distribution of Goal Completion Ratios\n(Projects with Goal > $5000, Ratio ≤ 10)', fontweight='bold')
ax1.legend()
ax1.grid(True, alpha=0.3)

# 2. Top 15 projects bar chart
ax2 = axes[0, 1]
top_15 = top_performers.head(15)
project_names = [name[:20] + "..." if len(name) > 20 else name for name in top_15['Name']]
bars = ax2.barh(range(len(top_15)), top_15['Goal_Completion_Ratio'], color='lightgreen')
ax2.set_yticks(range(len(top_15)))
ax2.set_yticklabels(project_names, fontsize=9)
ax2.set_xlabel('Goal Completion Ratio')
ax2.set_title('Top 15 Projects by Goal Completion Ratio\n(Goal > $5000)', fontweight='bold')
ax2.invert_yaxis()

# Add value labels
for i, bar in enumerate(bars):
    width = bar.get_width()
    ax2.text(width + 0.1, bar.get_y() + bar.get_height()/2,
             f'{width:.1f}', ha='left', va='center', fontsize=8)

# 3. Scatter plot: Goal vs Pledged for successful projects
ax3 = axes[1, 0]
successful_sample = successful_projects.sample(min(1000, len(successful_projects)))  # Sample for performance
scatter = ax3.scatter(successful_sample['Goal'], successful_sample['Pledged'], 
                     c=successful_sample['Goal_Completion_Ratio'], 
                     cmap='viridis', alpha=0.6, s=30)
ax3.plot([0, successful_sample['Goal'].max()], [0, successful_sample['Goal'].max()], 
         'r--', alpha=0.8, label='Goal = Pledged line')
ax3.set_xlabel('Goal ($)')
ax3.set_ylabel('Pledged ($)')
ax3.set_title('Goal vs Pledged for Successful Projects\n(Sample of projects with ratio > 1)', fontweight='bold')
ax3.legend()
plt.colorbar(scatter, ax=ax3, label='Goal Completion Ratio')
ax3.set_xscale('log')
ax3.set_yscale('log')

# 4. Category distribution for top performers
ax4 = axes[1, 1]
top_100_categories = top_performers.head(100)['Category'].value_counts()
ax4.pie(top_100_categories.values, labels=top_100_categories.index, autopct='%1.1f%%', startangle=90)
ax4.set_title('Category Distribution\n(Top 100 Projects by Goal Completion)', fontweight='bold')

plt.tight_layout()
plt.show()

# Analyze extreme performers (ratio > 5)
extreme_performers = high_goal_projects[high_goal_projects['Goal_Completion_Ratio'] > 5]

print(f"🚀 EXTREME PERFORMERS (Goal > $5000 AND Ratio > 5):")
print("=" * 60)
print(f"Number of extreme performers: {len(extreme_performers)}")

if len(extreme_performers) > 0:
    print(f"\nTop 10 extreme performers:")
    for i, (idx, project) in enumerate(extreme_performers.head(10).iterrows(), 1):
        ratio = project['Goal_Completion_Ratio']
        goal = project['Goal']
        pledged = project['Pledged']
        name = project['Name'][:40] + "..." if len(project['Name']) > 40 else project['Name']
        
        print(f"{i:2d}. {name}")
        print(f"    Ratio: {ratio:.1f}x | Goal: ${goal:,.0f} | Pledged: ${pledged:,.0f}")
        print(f"    Category: {project['Category']} | Backers: {project['Backers']:,}")
        print()
else:
    print("No projects found with ratio > 5")

# Compare with overall dataset statistics
print("📈 COMPARISON WITH OVERALL DATASET:")
print("=" * 50)

# Calculate ratios for all projects
df['Goal_Completion_Ratio'] = df['Pledged'] / df['Goal']
all_successful = df[df['Goal_Completion_Ratio'] > 1]

print(f"Overall success rate (all projects): {len(all_successful)/len(df)*100:.2f}%")
print(f"Success rate for projects with goal > $5000: {len(successful_projects)/len(high_goal_projects)*100:.2f}%")
print(f"\nAverage goal completion ratio:")
print(f"All projects: {df['Goal_Completion_Ratio'].mean():.3f}")
print(f"Projects with goal > $5000: {high_goal_projects['Goal_Completion_Ratio'].mean():.3f}")
print(f"\nMedian goal completion ratio:")
print(f"All projects: {df['Goal_Completion_Ratio'].median():.3f}")
print(f"Projects with goal > $5000: {high_goal_projects['Goal_Completion_Ratio'].median():.3f}")

# Final summary
print("=" * 80)
print("🎯 FINAL SUMMARY")
print("=" * 80)

print(f"\n🏆 MAIN ANSWER:")
print(f"The project with goal > $5000 that had the biggest goal completion is:")
print(f"'{top_project['Name']}'")
print(f"\n📊 KEY METRICS:")
print(f"• Goal: ${top_project['Goal']:,.2f}")
print(f"• Pledged: ${top_project['Pledged']:,.2f}")
print(f"• Goal Completion Ratio: {top_project['Goal_Completion_Ratio']:.2f}")
print(f"• Goal Completion Percentage: {top_project['Goal_Completion_Percentage']:.1f}%")
print(f"• Amount raised over goal: ${top_project['Pledged'] - top_project['Goal']:,.2f}")
print(f"• Number of backers: {top_project['Backers']:,}")
print(f"• Category: {top_project['Category']} - {top_project['Subcategory']}")
print(f"• Country: {top_project['Country']}")

print(f"\n📈 CONTEXT:")
print(f"• Total projects analyzed: {len(high_goal_projects):,} (goal > $5000)")
print(f"• Projects that exceeded their goal: {len(successful_projects):,} ({len(successful_projects)/len(high_goal_projects)*100:.1f}%)")
print(f"• Average goal completion ratio for successful projects: {successful_projects['Goal_Completion_Ratio'].mean():.2f}")
print(f"• This project exceeded its goal by {top_project['Goal_Completion_Ratio']:.1f}x")