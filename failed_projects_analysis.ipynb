# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)

# Load the data
df = pd.read_csv('kickstarter_projects (3) (1).csv')

# Display basic information about the dataset
print("Dataset Shape:", df.shape)
print("\nColumn Names:")
print(df.columns.tolist())
print("\nFirst few rows:")
df.head()

# Check the data types and missing values
print("Data Types:")
print(df.dtypes)
print("\nMissing Values:")
print(df.isnull().sum())
print("\nUnique values in 'State' column:")
print(df['State'].value_counts())

# Convert the 'Launched' column to datetime
df['Launched'] = pd.to_datetime(df['Launched'])

# Extract year and month from the launched date
df['Year'] = df['Launched'].dt.year
df['Month'] = df['Launched'].dt.month
df['Month_Name'] = df['Launched'].dt.month_name()
df['Year_Month'] = df['Launched'].dt.to_period('M')

print("Date processing completed!")
print("\nDate range in the dataset:")
print(f"From: {df['Launched'].min()}")
print(f"To: {df['Launched'].max()}")

# Filter only failed projects
failed_projects = df[df['State'] == 'Failed'].copy()

print(f"Total projects: {len(df):,}")
print(f"Failed projects: {len(failed_projects):,}")
print(f"Percentage of failed projects: {len(failed_projects)/len(df)*100:.2f}%")

# Group by Year-Month and count failed projects
failed_by_month_year = failed_projects.groupby('Year_Month').size().reset_index(name='Failed_Count')
failed_by_month_year['Year_Month_Str'] = failed_by_month_year['Year_Month'].astype(str)

# Sort by failed count in descending order
failed_by_month_year_sorted = failed_by_month_year.sort_values('Failed_Count', ascending=False)

print("Top 10 months with highest number of failed projects:")
print(failed_by_month_year_sorted.head(10))

# Find the month and year with the highest number of failed projects
max_failed_row = failed_by_month_year_sorted.iloc[0]
max_failed_period = max_failed_row['Year_Month']
max_failed_count = max_failed_row['Failed_Count']

print(f"\n🔍 ANSWER: The month and year with the highest number of failed projects is:")
print(f"📅 Period: {max_failed_period} ({max_failed_period.strftime('%B %Y')})")
print(f"❌ Number of failed projects: {max_failed_count:,}")

# Get some additional context
year = max_failed_period.year
month = max_failed_period.month
total_projects_that_month = len(df[(df['Year'] == year) & (df['Month'] == month)])
failure_rate = (max_failed_count / total_projects_that_month) * 100

print(f"\n📊 Additional Context:")
print(f"Total projects launched in {max_failed_period.strftime('%B %Y')}: {total_projects_that_month:,}")
print(f"Failure rate for that month: {failure_rate:.2f}%")

# Create visualizations
fig, axes = plt.subplots(2, 2, figsize=(20, 16))

# 1. Time series plot of failed projects by month-year
ax1 = axes[0, 0]
failed_by_month_year['Year_Month_Date'] = failed_by_month_year['Year_Month'].dt.to_timestamp()
ax1.plot(failed_by_month_year['Year_Month_Date'], failed_by_month_year['Failed_Count'], 
         marker='o', linewidth=2, markersize=4)
ax1.set_title('Failed Projects Over Time (Monthly)', fontsize=14, fontweight='bold')
ax1.set_xlabel('Year-Month')
ax1.set_ylabel('Number of Failed Projects')
ax1.grid(True, alpha=0.3)
ax1.tick_params(axis='x', rotation=45)

# Highlight the maximum point
max_date = failed_by_month_year[failed_by_month_year['Failed_Count'] == max_failed_count]['Year_Month_Date'].iloc[0]
ax1.scatter(max_date, max_failed_count, color='red', s=100, zorder=5)
ax1.annotate(f'Max: {max_failed_count}\n{max_failed_period.strftime("%b %Y")}', 
             xy=(max_date, max_failed_count), xytext=(10, 10),
             textcoords='offset points', fontsize=10, 
             bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
             arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

# 2. Top 15 months with most failed projects (bar chart)
ax2 = axes[0, 1]
top_15 = failed_by_month_year_sorted.head(15)
bars = ax2.bar(range(len(top_15)), top_15['Failed_Count'], 
               color=['red' if i == 0 else 'skyblue' for i in range(len(top_15))])
ax2.set_title('Top 15 Months with Most Failed Projects', fontsize=14, fontweight='bold')
ax2.set_xlabel('Rank')
ax2.set_ylabel('Number of Failed Projects')
ax2.set_xticks(range(len(top_15)))
ax2.set_xticklabels([str(period) for period in top_15['Year_Month']], rotation=45, ha='right')

# Add value labels on bars
for i, bar in enumerate(bars):
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height + 10,
             f'{int(height)}', ha='center', va='bottom', fontsize=9)

# 3. Failed projects by year
ax3 = axes[1, 0]
failed_by_year = failed_projects.groupby('Year').size().reset_index(name='Failed_Count')
ax3.bar(failed_by_year['Year'], failed_by_year['Failed_Count'], color='lightcoral')
ax3.set_title('Failed Projects by Year', fontsize=14, fontweight='bold')
ax3.set_xlabel('Year')
ax3.set_ylabel('Number of Failed Projects')
ax3.tick_params(axis='x', rotation=45)

# 4. Failed projects by month (aggregated across all years)
ax4 = axes[1, 1]
failed_by_month = failed_projects.groupby('Month').size().reset_index(name='Failed_Count')
month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
               'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
ax4.bar(failed_by_month['Month'], failed_by_month['Failed_Count'], color='lightgreen')
ax4.set_title('Failed Projects by Month (All Years Combined)', fontsize=14, fontweight='bold')
ax4.set_xlabel('Month')
ax4.set_ylabel('Number of Failed Projects')
ax4.set_xticks(range(1, 13))
ax4.set_xticklabels(month_names)

plt.tight_layout()
plt.show()

# Create a heatmap showing failed projects by year and month
plt.figure(figsize=(14, 8))

# Create a pivot table for the heatmap
heatmap_data = failed_projects.groupby(['Year', 'Month']).size().unstack(fill_value=0)

# Create the heatmap
sns.heatmap(heatmap_data, annot=True, fmt='d', cmap='Reds', 
            cbar_kws={'label': 'Number of Failed Projects'},
            xticklabels=['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'])

plt.title('Heatmap: Failed Projects by Year and Month', fontsize=16, fontweight='bold', pad=20)
plt.xlabel('Month', fontsize=12)
plt.ylabel('Year', fontsize=12)
plt.tight_layout()
plt.show()

# Summary statistics and insights
print("=" * 60)
print("📈 SUMMARY STATISTICS AND INSIGHTS")
print("=" * 60)

print(f"\n🎯 KEY FINDING:")
print(f"The month and year with the highest number of failed projects is {max_failed_period.strftime('%B %Y')}")
print(f"with {max_failed_count:,} failed projects.")

print(f"\n📊 DATASET OVERVIEW:")
print(f"• Total projects in dataset: {len(df):,}")
print(f"• Total failed projects: {len(failed_projects):,}")
print(f"• Overall failure rate: {len(failed_projects)/len(df)*100:.2f}%")
print(f"• Date range: {df['Launched'].min().strftime('%B %Y')} to {df['Launched'].max().strftime('%B %Y')}")

print(f"\n🔝 TOP 5 MONTHS WITH MOST FAILED PROJECTS:")
for i, row in failed_by_month_year_sorted.head(5).iterrows():
    period = row['Year_Month']
    count = row['Failed_Count']
    print(f"  {i+1}. {period.strftime('%B %Y')}: {count:,} failed projects")

print(f"\n📅 YEARLY TRENDS:")
yearly_stats = failed_by_year.sort_values('Failed_Count', ascending=False)
print(f"• Year with most failed projects: {yearly_stats.iloc[0]['Year']} ({yearly_stats.iloc[0]['Failed_Count']:,} failures)")
print(f"• Year with least failed projects: {yearly_stats.iloc[-1]['Year']} ({yearly_stats.iloc[-1]['Failed_Count']:,} failures)")

print(f"\n🗓️ MONTHLY PATTERNS (across all years):")
monthly_stats = failed_by_month.sort_values('Failed_Count', ascending=False)
worst_month = monthly_stats.iloc[0]['Month']
best_month = monthly_stats.iloc[-1]['Month']
print(f"• Month with most failures: {month_names[worst_month-1]} ({monthly_stats.iloc[0]['Failed_Count']:,} failures)")
print(f"• Month with least failures: {month_names[best_month-1]} ({monthly_stats.iloc[-1]['Failed_Count']:,} failures)")