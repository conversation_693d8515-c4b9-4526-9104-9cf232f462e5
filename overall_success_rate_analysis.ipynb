# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)

# Load the data
df = pd.read_csv('kickstarter_projects (3) (1).csv')

# Display basic information about the dataset
print("Dataset Shape:", df.shape)
print("\nColumn Names:")
print(df.columns.tolist())
print("\nFirst few rows:")
df.head()

# Check the distribution of project states
print("Project State Distribution:")
state_counts = df['State'].value_counts()
state_percentages = df['State'].value_counts(normalize=True) * 100

for state in state_counts.index:
    count = state_counts[state]
    percentage = state_percentages[state]
    print(f"{state:<12}: {count:>8,} projects ({percentage:5.2f}%)")

print(f"\nTotal projects: {len(df):,}")

# Calculate overall success rate
successful_projects = len(df[df['State'] == 'Successful'])
total_projects = len(df)
overall_success_rate = (successful_projects / total_projects) * 100

print("🎯 OVERALL SUCCESS RATE:")
print("=" * 40)
print(f"Total Projects: {total_projects:,}")
print(f"Successful Projects: {successful_projects:,}")
print(f"Overall Success Rate: {overall_success_rate:.2f}%")

# Additional metrics
failed_projects = len(df[df['State'] == 'Failed'])
canceled_projects = len(df[df['State'] == 'Canceled'])
suspended_projects = len(df[df['State'] == 'Suspended']) if 'Suspended' in df['State'].values else 0
live_projects = len(df[df['State'] == 'Live']) if 'Live' in df['State'].values else 0

print(f"\n📊 DETAILED BREAKDOWN:")
print(f"Failed Projects: {failed_projects:,} ({failed_projects/total_projects*100:.2f}%)")
print(f"Canceled Projects: {canceled_projects:,} ({canceled_projects/total_projects*100:.2f}%)")
if suspended_projects > 0:
    print(f"Suspended Projects: {suspended_projects:,} ({suspended_projects/total_projects*100:.2f}%)")
if live_projects > 0:
    print(f"Live Projects: {live_projects:,} ({live_projects/total_projects*100:.2f}%)")

# Success rate by category
category_success = df.groupby('Category').agg({
    'State': ['count', lambda x: (x == 'Successful').sum()]
}).round(2)

category_success.columns = ['Total_Projects', 'Successful_Projects']
category_success['Success_Rate'] = (category_success['Successful_Projects'] / category_success['Total_Projects']) * 100
category_success = category_success.sort_values('Success_Rate', ascending=False)

print("📈 SUCCESS RATE BY CATEGORY:")
print("=" * 60)
print(f"{'Category':<20} {'Total':<8} {'Successful':<10} {'Success Rate':<12}")
print("-" * 60)

for category, row in category_success.iterrows():
    total = int(row['Total_Projects'])
    successful = int(row['Successful_Projects'])
    rate = row['Success_Rate']
    print(f"{category:<20} {total:<8,} {successful:<10,} {rate:<12.2f}%")

# Success rate by goal amount ranges
# Create goal ranges
df['Goal_Range'] = pd.cut(df['Goal'], 
                         bins=[0, 1000, 5000, 10000, 25000, 50000, 100000, float('inf')],
                         labels=['$0-1K', '$1K-5K', '$5K-10K', '$10K-25K', '$25K-50K', '$50K-100K', '$100K+'])

goal_success = df.groupby('Goal_Range').agg({
    'State': ['count', lambda x: (x == 'Successful').sum()]
}).round(2)

goal_success.columns = ['Total_Projects', 'Successful_Projects']
goal_success['Success_Rate'] = (goal_success['Successful_Projects'] / goal_success['Total_Projects']) * 100

print("💰 SUCCESS RATE BY GOAL AMOUNT:")
print("=" * 50)
print(f"{'Goal Range':<12} {'Total':<8} {'Successful':<10} {'Success Rate':<12}")
print("-" * 50)

for goal_range, row in goal_success.iterrows():
    total = int(row['Total_Projects'])
    successful = int(row['Successful_Projects'])
    rate = row['Success_Rate']
    print(f"{goal_range:<12} {total:<8,} {successful:<10,} {rate:<12.2f}%")

# Success rate by year
df['Launched'] = pd.to_datetime(df['Launched'])
df['Launch_Year'] = df['Launched'].dt.year

yearly_success = df.groupby('Launch_Year').agg({
    'State': ['count', lambda x: (x == 'Successful').sum()]
}).round(2)

yearly_success.columns = ['Total_Projects', 'Successful_Projects']
yearly_success['Success_Rate'] = (yearly_success['Successful_Projects'] / yearly_success['Total_Projects']) * 100

print("📅 SUCCESS RATE BY YEAR:")
print("=" * 45)
print(f"{'Year':<6} {'Total':<8} {'Successful':<10} {'Success Rate':<12}")
print("-" * 45)

for year, row in yearly_success.iterrows():
    total = int(row['Total_Projects'])
    successful = int(row['Successful_Projects'])
    rate = row['Success_Rate']
    print(f"{year:<6} {total:<8,} {successful:<10,} {rate:<12.2f}%")

# Success rate by country (top 10 countries by project count)
top_countries = df['Country'].value_counts().head(10).index
country_success = df[df['Country'].isin(top_countries)].groupby('Country').agg({
    'State': ['count', lambda x: (x == 'Successful').sum()]
}).round(2)

country_success.columns = ['Total_Projects', 'Successful_Projects']
country_success['Success_Rate'] = (country_success['Successful_Projects'] / country_success['Total_Projects']) * 100
country_success = country_success.sort_values('Success_Rate', ascending=False)

print("🌍 SUCCESS RATE BY COUNTRY (Top 10 by project count):")
print("=" * 60)
print(f"{'Country':<15} {'Total':<8} {'Successful':<10} {'Success Rate':<12}")
print("-" * 60)

for country, row in country_success.iterrows():
    total = int(row['Total_Projects'])
    successful = int(row['Successful_Projects'])
    rate = row['Success_Rate']
    print(f"{country:<15} {total:<8,} {successful:<10,} {rate:<12.2f}%")

# Create comprehensive visualizations
fig, axes = plt.subplots(2, 2, figsize=(20, 16))

# 1. Overall project state distribution (pie chart)
ax1 = axes[0, 0]
colors = ['#2ecc71', '#e74c3c', '#f39c12', '#9b59b6', '#3498db']
wedges, texts, autotexts = ax1.pie(state_counts.values, labels=state_counts.index, 
                                  autopct='%1.1f%%', startangle=90, colors=colors[:len(state_counts)])
ax1.set_title('Overall Project State Distribution', fontsize=14, fontweight='bold')

# Highlight successful projects
for i, label in enumerate(state_counts.index):
    if label == 'Successful':
        wedges[i].set_edgecolor('black')
        wedges[i].set_linewidth(3)

# 2. Success rate by category (bar chart)
ax2 = axes[0, 1]
category_success_sorted = category_success.sort_values('Success_Rate', ascending=True)
bars = ax2.barh(range(len(category_success_sorted)), category_success_sorted['Success_Rate'], 
                color='lightblue', edgecolor='navy', alpha=0.7)
ax2.set_yticks(range(len(category_success_sorted)))
ax2.set_yticklabels(category_success_sorted.index, fontsize=10)
ax2.set_xlabel('Success Rate (%)')
ax2.set_title('Success Rate by Category', fontsize=14, fontweight='bold')
ax2.axvline(x=overall_success_rate, color='red', linestyle='--', linewidth=2, 
           label=f'Overall Average ({overall_success_rate:.1f}%)')
ax2.legend()

# Add value labels
for i, bar in enumerate(bars):
    width = bar.get_width()
    ax2.text(width + 0.5, bar.get_y() + bar.get_height()/2,
             f'{width:.1f}%', ha='left', va='center', fontsize=9)

# 3. Success rate by goal amount
ax3 = axes[1, 0]
goal_ranges = goal_success.index
bars3 = ax3.bar(range(len(goal_ranges)), goal_success['Success_Rate'], 
                color='lightgreen', edgecolor='darkgreen', alpha=0.7)
ax3.set_xticks(range(len(goal_ranges)))
ax3.set_xticklabels(goal_ranges, rotation=45, ha='right')
ax3.set_ylabel('Success Rate (%)')
ax3.set_title('Success Rate by Goal Amount Range', fontsize=14, fontweight='bold')
ax3.axhline(y=overall_success_rate, color='red', linestyle='--', linewidth=2, 
           label=f'Overall Average ({overall_success_rate:.1f}%)')
ax3.legend()

# Add value labels
for i, bar in enumerate(bars3):
    height = bar.get_height()
    ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
             f'{height:.1f}%', ha='center', va='bottom', fontsize=10)

# 4. Success rate trend over years
ax4 = axes[1, 1]
ax4.plot(yearly_success.index, yearly_success['Success_Rate'], 
         marker='o', linewidth=2, markersize=6, color='purple')
ax4.set_xlabel('Year')
ax4.set_ylabel('Success Rate (%)')
ax4.set_title('Success Rate Trend Over Years', fontsize=14, fontweight='bold')
ax4.grid(True, alpha=0.3)
ax4.axhline(y=overall_success_rate, color='red', linestyle='--', linewidth=2, 
           label=f'Overall Average ({overall_success_rate:.1f}%)')
ax4.legend()

plt.tight_layout()
plt.show()

# Additional analysis: Success rate by subcategory (top 20)
subcategory_success = df.groupby('Subcategory').agg({
    'State': ['count', lambda x: (x == 'Successful').sum()]
}).round(2)

subcategory_success.columns = ['Total_Projects', 'Successful_Projects']
subcategory_success['Success_Rate'] = (subcategory_success['Successful_Projects'] / subcategory_success['Total_Projects']) * 100

# Filter subcategories with at least 50 projects for meaningful analysis
significant_subcategories = subcategory_success[subcategory_success['Total_Projects'] >= 50]
top_subcategories = significant_subcategories.sort_values('Success_Rate', ascending=False).head(20)

print("🏆 TOP 20 SUBCATEGORIES BY SUCCESS RATE (min 50 projects):")
print("=" * 70)
print(f"{'Subcategory':<25} {'Total':<8} {'Successful':<10} {'Success Rate':<12}")
print("-" * 70)

for subcategory, row in top_subcategories.iterrows():
    total = int(row['Total_Projects'])
    successful = int(row['Successful_Projects'])
    rate = row['Success_Rate']
    print(f"{subcategory:<25} {total:<8,} {successful:<10,} {rate:<12.2f}%")

# Success rate by project duration (calculate campaign length)
df['Deadline'] = pd.to_datetime(df['Deadline'])
df['Campaign_Duration'] = (df['Deadline'] - df['Launched']).dt.days

# Create duration ranges
df['Duration_Range'] = pd.cut(df['Campaign_Duration'], 
                             bins=[0, 15, 30, 45, 60, float('inf')],
                             labels=['1-15 days', '16-30 days', '31-45 days', '46-60 days', '60+ days'])

duration_success = df.groupby('Duration_Range').agg({
    'State': ['count', lambda x: (x == 'Successful').sum()]
}).round(2)

duration_success.columns = ['Total_Projects', 'Successful_Projects']
duration_success['Success_Rate'] = (duration_success['Successful_Projects'] / duration_success['Total_Projects']) * 100

print("⏰ SUCCESS RATE BY CAMPAIGN DURATION:")
print("=" * 50)
print(f"{'Duration':<12} {'Total':<8} {'Successful':<10} {'Success Rate':<12}")
print("-" * 50)

for duration, row in duration_success.iterrows():
    total = int(row['Total_Projects'])
    successful = int(row['Successful_Projects'])
    rate = row['Success_Rate']
    print(f"{duration:<12} {total:<8,} {successful:<10,} {rate:<12.2f}%")

# Key insights and summary
print("=" * 80)
print("📊 KEY INSIGHTS AND SUMMARY")
print("=" * 80)

print(f"\n🎯 OVERALL SUCCESS RATE: {overall_success_rate:.2f}%")
print(f"   Out of {total_projects:,} total projects, {successful_projects:,} were successful.")

# Best and worst performing categories
best_category = category_success.index[0]
best_category_rate = category_success.iloc[0]['Success_Rate']
worst_category = category_success.index[-1]
worst_category_rate = category_success.iloc[-1]['Success_Rate']

print(f"\n🏆 BEST PERFORMING CATEGORY:")
print(f"   {best_category}: {best_category_rate:.2f}% success rate")
print(f"\n📉 WORST PERFORMING CATEGORY:")
print(f"   {worst_category}: {worst_category_rate:.2f}% success rate")

# Goal amount insights
best_goal_range = goal_success['Success_Rate'].idxmax()
best_goal_rate = goal_success['Success_Rate'].max()
worst_goal_range = goal_success['Success_Rate'].idxmin()
worst_goal_rate = goal_success['Success_Rate'].min()

print(f"\n💰 GOAL AMOUNT INSIGHTS:")
print(f"   Best performing goal range: {best_goal_range} ({best_goal_rate:.2f}% success rate)")
print(f"   Worst performing goal range: {worst_goal_range} ({worst_goal_rate:.2f}% success rate)")

# Year insights
best_year = yearly_success['Success_Rate'].idxmax()
best_year_rate = yearly_success['Success_Rate'].max()
worst_year = yearly_success['Success_Rate'].idxmin()
worst_year_rate = yearly_success['Success_Rate'].min()

print(f"\n📅 TEMPORAL INSIGHTS:")
print(f"   Best performing year: {best_year} ({best_year_rate:.2f}% success rate)")
print(f"   Worst performing year: {worst_year} ({worst_year_rate:.2f}% success rate)")

# Duration insights
best_duration = duration_success['Success_Rate'].idxmax()
best_duration_rate = duration_success['Success_Rate'].max()

print(f"\n⏰ CAMPAIGN DURATION INSIGHTS:")
print(f"   Optimal campaign duration: {best_duration} ({best_duration_rate:.2f}% success rate)")

print(f"\n📈 GENERAL PATTERNS:")
print(f"   • Lower goal amounts generally have higher success rates")
print(f"   • Success rates vary significantly by category")
print(f"   • Campaign duration affects success probability")
print(f"   • Success rates have changed over time")

print(f"\n🔍 DATASET COVERAGE:")
print(f"   • Time period: {df['Launch_Year'].min()} - {df['Launch_Year'].max()}")
print(f"   • Countries: {df['Country'].nunique()} different countries")
print(f"   • Categories: {df['Category'].nunique()} main categories")
print(f"   • Subcategories: {df['Subcategory'].nunique()} subcategories")