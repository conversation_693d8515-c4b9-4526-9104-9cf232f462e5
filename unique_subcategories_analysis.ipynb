# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)

# Load the data
df = pd.read_csv('kickstarter_projects (3) (1).csv')

# Display basic information about the dataset
print("Dataset Shape:", df.shape)
print("\nColumn Names:")
print(df.columns.tolist())
print("\nFirst few rows:")
df.head()

# Check for missing values in the Subcategory column
print("Missing values in Subcategory column:", df['Subcategory'].isnull().sum())
print("Total rows in dataset:", len(df))
print("\nSample of subcategories:")
print(df['Subcategory'].head(10).tolist())

# Find the number of unique subcategories
unique_subcategories = df['Subcategory'].nunique()
unique_subcategories_list = df['Subcategory'].unique()

print("🎯 ANSWER:")
print(f"Number of unique subcategories: {unique_subcategories}")
print(f"\n📊 Additional Information:")
print(f"Total projects: {len(df):,}")
print(f"Unique subcategories: {unique_subcategories}")
print(f"Average projects per subcategory: {len(df)/unique_subcategories:.1f}")

# Display all unique subcategories in alphabetical order
sorted_subcategories = sorted(unique_subcategories_list)

print("📝 Complete list of all unique subcategories (alphabetically sorted):")
print("=" * 70)

# Display in columns for better readability
for i, subcategory in enumerate(sorted_subcategories, 1):
    print(f"{i:3d}. {subcategory}")

# Analyze subcategory distribution
subcategory_counts = df['Subcategory'].value_counts()

print("📈 Top 20 most popular subcategories:")
print("=" * 50)
for i, (subcategory, count) in enumerate(subcategory_counts.head(20).items(), 1):
    percentage = (count / len(df)) * 100
    print(f"{i:2d}. {subcategory:<25} : {count:>6,} projects ({percentage:5.2f}%)")

# Analyze subcategory distribution statistics
print("📊 Subcategory Distribution Statistics:")
print("=" * 40)
print(f"Most popular subcategory: {subcategory_counts.index[0]} ({subcategory_counts.iloc[0]:,} projects)")
print(f"Least popular subcategories: {subcategory_counts[subcategory_counts == 1].count()} subcategories with only 1 project")
print(f"\nDistribution statistics:")
print(f"Mean projects per subcategory: {subcategory_counts.mean():.1f}")
print(f"Median projects per subcategory: {subcategory_counts.median():.1f}")
print(f"Standard deviation: {subcategory_counts.std():.1f}")
print(f"\nSubcategories with:")
print(f"• 1 project: {len(subcategory_counts[subcategory_counts == 1])}")
print(f"• 2-10 projects: {len(subcategory_counts[(subcategory_counts >= 2) & (subcategory_counts <= 10)])}")
print(f"• 11-100 projects: {len(subcategory_counts[(subcategory_counts >= 11) & (subcategory_counts <= 100)])}")
print(f"• 101-1000 projects: {len(subcategory_counts[(subcategory_counts >= 101) & (subcategory_counts <= 1000)])}")
print(f"• 1000+ projects: {len(subcategory_counts[subcategory_counts > 1000])}")

# Create visualizations
fig, axes = plt.subplots(2, 2, figsize=(20, 16))

# 1. Top 20 subcategories bar chart
ax1 = axes[0, 0]
top_20 = subcategory_counts.head(20)
bars = ax1.barh(range(len(top_20)), top_20.values, color='skyblue')
ax1.set_yticks(range(len(top_20)))
ax1.set_yticklabels(top_20.index, fontsize=10)
ax1.set_xlabel('Number of Projects')
ax1.set_title('Top 20 Most Popular Subcategories', fontsize=14, fontweight='bold')
ax1.invert_yaxis()

# Add value labels on bars
for i, bar in enumerate(bars):
    width = bar.get_width()
    ax1.text(width + 50, bar.get_y() + bar.get_height()/2,
             f'{int(width):,}', ha='left', va='center', fontsize=9)

# 2. Distribution histogram
ax2 = axes[0, 1]
ax2.hist(subcategory_counts.values, bins=50, color='lightgreen', alpha=0.7, edgecolor='black')
ax2.set_xlabel('Number of Projects per Subcategory')
ax2.set_ylabel('Number of Subcategories')
ax2.set_title('Distribution of Projects per Subcategory', fontsize=14, fontweight='bold')
ax2.set_yscale('log')
ax2.grid(True, alpha=0.3)

# 3. Cumulative distribution
ax3 = axes[1, 0]
sorted_counts = subcategory_counts.sort_values(ascending=False)
cumulative_projects = sorted_counts.cumsum()
cumulative_percentage = (cumulative_projects / len(df)) * 100

ax3.plot(range(1, len(cumulative_percentage) + 1), cumulative_percentage, color='red', linewidth=2)
ax3.set_xlabel('Subcategory Rank (by popularity)')
ax3.set_ylabel('Cumulative Percentage of Projects')
ax3.set_title('Cumulative Distribution of Projects by Subcategory', fontsize=14, fontweight='bold')
ax3.grid(True, alpha=0.3)
ax3.axhline(y=80, color='orange', linestyle='--', alpha=0.7, label='80% of projects')
ax3.legend()

# 4. Pie chart of subcategory size groups
ax4 = axes[1, 1]
size_groups = {
    '1 project': len(subcategory_counts[subcategory_counts == 1]),
    '2-10 projects': len(subcategory_counts[(subcategory_counts >= 2) & (subcategory_counts <= 10)]),
    '11-100 projects': len(subcategory_counts[(subcategory_counts >= 11) & (subcategory_counts <= 100)]),
    '101-1000 projects': len(subcategory_counts[(subcategory_counts >= 101) & (subcategory_counts <= 1000)]),
    '1000+ projects': len(subcategory_counts[subcategory_counts > 1000])
}

# Remove groups with 0 subcategories
size_groups = {k: v for k, v in size_groups.items() if v > 0}

ax4.pie(size_groups.values(), labels=size_groups.keys(), autopct='%1.1f%%', startangle=90)
ax4.set_title('Distribution of Subcategories by Size', fontsize=14, fontweight='bold')

plt.tight_layout()
plt.show()

# Analyze relationship between categories and subcategories
category_subcategory = df.groupby('Category')['Subcategory'].nunique().sort_values(ascending=False)

print("🏷️ Number of subcategories per main category:")
print("=" * 50)
for category, count in category_subcategory.items():
    print(f"{category:<20} : {count:>3} subcategories")

print(f"\nTotal unique categories: {df['Category'].nunique()}")
print(f"Average subcategories per category: {category_subcategory.mean():.1f}")

# Show some examples of subcategories for each main category
print("🔍 Sample subcategories for each main category:")
print("=" * 60)

for category in sorted(df['Category'].unique()):
    subcategories = df[df['Category'] == category]['Subcategory'].unique()
    subcategories_sample = sorted(subcategories)[:5]  # Show first 5 alphabetically
    total_subcats = len(subcategories)
    
    print(f"\n{category} ({total_subcats} subcategories):")
    for subcat in subcategories_sample:
        count = len(df[(df['Category'] == category) & (df['Subcategory'] == subcat)])
        print(f"  • {subcat} ({count:,} projects)")
    
    if total_subcats > 5:
        print(f"  ... and {total_subcats - 5} more")

# Final summary
print("=" * 70)
print("📋 FINAL SUMMARY")
print("=" * 70)

print(f"\n🎯 MAIN ANSWER:")
print(f"There are {unique_subcategories} unique subcategories in the Kickstarter dataset.")

print(f"\n📊 KEY STATISTICS:")
print(f"• Total projects: {len(df):,}")
print(f"• Unique categories: {df['Category'].nunique()}")
print(f"• Unique subcategories: {unique_subcategories}")
print(f"• Average subcategories per category: {category_subcategory.mean():.1f}")
print(f"• Average projects per subcategory: {len(df)/unique_subcategories:.1f}")

print(f"\n🏆 TOP INSIGHTS:")
print(f"• Most popular subcategory: '{subcategory_counts.index[0]}' with {subcategory_counts.iloc[0]:,} projects")
print(f"• Category with most subcategories: '{category_subcategory.index[0]}' with {category_subcategory.iloc[0]} subcategories")
print(f"• {len(subcategory_counts[subcategory_counts == 1])} subcategories have only 1 project")
print(f"• Top 20 subcategories account for {(subcategory_counts.head(20).sum()/len(df)*100):.1f}% of all projects")

# Find the 80/20 rule point
cumulative_pct = (subcategory_counts.sort_values(ascending=False).cumsum() / len(df)) * 100
subcats_for_80_pct = len(cumulative_pct[cumulative_pct <= 80])
print(f"• {subcats_for_80_pct} subcategories ({subcats_for_80_pct/unique_subcategories*100:.1f}%) account for 80% of all projects")