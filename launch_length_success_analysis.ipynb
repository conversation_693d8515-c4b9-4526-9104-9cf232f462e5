# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)

# Load the data
df = pd.read_csv('kickstarter_projects (3) (1).csv')

# Display basic information about the dataset
print("Dataset Shape:", df.shape)
print("\nColumn Names:")
print(df.columns.tolist())
print("\nFirst few rows:")
df.head()

# Calculate campaign duration
df['Launched'] = pd.to_datetime(df['Launched'])
df['Deadline'] = pd.to_datetime(df['Deadline'])
df['Campaign_Duration'] = (df['Deadline'] - df['Launched']).dt.days

# Check for any invalid durations
print("Campaign Duration Statistics:")
print(df['Campaign_Duration'].describe())
print(f"\nMissing values: {df['Campaign_Duration'].isnull().sum()}")
print(f"Negative durations: {len(df[df['Campaign_Duration'] < 0])}")
print(f"Zero duration: {len(df[df['Campaign_Duration'] == 0])}")

# Remove any invalid durations
df = df[df['Campaign_Duration'] > 0]
print(f"\nDataset after removing invalid durations: {len(df):,} projects")

# Create the specified duration categories
def categorize_duration(days):
    if 1 <= days <= 10:
        return 'a. 1-10 days'
    elif 11 <= days <= 20:
        return 'b. 11-20 days'
    elif 21 <= days <= 30:
        return 'c. 21-30 days'
    elif days > 40:
        return 'd. >40 days'
    else:
        return 'Other (31-40 days)'

df['Duration_Category'] = df['Campaign_Duration'].apply(categorize_duration)

# Show distribution of duration categories
print("Distribution of Campaign Duration Categories:")
duration_counts = df['Duration_Category'].value_counts().sort_index()
for category, count in duration_counts.items():
    percentage = (count / len(df)) * 100
    print(f"{category:<20}: {count:>8,} projects ({percentage:5.2f}%)")

print(f"\nTotal projects: {len(df):,}")

# Calculate overall success rate
overall_success_rate = (len(df[df['State'] == 'Successful']) / len(df)) * 100
print(f"Overall success rate: {overall_success_rate:.2f}%")
print(f"\nProject state distribution:")
print(df['State'].value_counts())

# Analyze success rate by duration category
duration_analysis = df.groupby('Duration_Category').agg({
    'State': ['count', lambda x: (x == 'Successful').sum()]
}).round(2)

duration_analysis.columns = ['Total_Projects', 'Successful_Projects']
duration_analysis['Success_Rate'] = (duration_analysis['Successful_Projects'] / duration_analysis['Total_Projects']) * 100
duration_analysis['Failed_Projects'] = duration_analysis['Total_Projects'] - duration_analysis['Successful_Projects']
duration_analysis['Project_Share'] = (duration_analysis['Total_Projects'] / len(df)) * 100
duration_analysis['Success_Rate_Difference'] = duration_analysis['Success_Rate'] - overall_success_rate

# Sort by the specified order (a, b, c, d)
specified_categories = ['a. 1-10 days', 'b. 11-20 days', 'c. 21-30 days', 'd. >40 days']
specified_analysis = duration_analysis.loc[specified_categories]

print("📊 SUCCESS RATE ANALYSIS BY LAUNCH LENGTH:")
print("=" * 85)
print(f"{'Category':<20} {'Total':<8} {'Success':<8} {'Failed':<8} {'Success%':<9} {'Difference':<10}")
print("-" * 85)

for category, row in specified_analysis.iterrows():
    total = int(row['Total_Projects'])
    successful = int(row['Successful_Projects'])
    failed = int(row['Failed_Projects'])
    success_rate = row['Success_Rate']
    difference = row['Success_Rate_Difference']
    print(f"{category:<20} {total:<8,} {successful:<8,} {failed:<8,} {success_rate:<9.2f} {difference:+9.2f}")

# Include "Other" category for completeness
if 'Other (31-40 days)' in duration_analysis.index:
    other_row = duration_analysis.loc['Other (31-40 days)']
    print(f"{'Other (31-40 days)':<20} {int(other_row['Total_Projects']):<8,} {int(other_row['Successful_Projects']):<8,} {int(other_row['Failed_Projects']):<8,} {other_row['Success_Rate']:<9.2f} {other_row['Success_Rate_Difference']:+9.2f}")

# Find the category with highest success rate
best_category = specified_analysis['Success_Rate'].idxmax()
best_success_rate = specified_analysis['Success_Rate'].max()
best_stats = specified_analysis.loc[best_category]

print(f"\n🎯 ANSWER: Launch length with highest success rate:")
print(f"'{best_category}' has the highest success rate at {best_success_rate:.2f}%")
print(f"\n📊 DETAILED STATISTICS FOR WINNER:")
print(f"• Total projects: {int(best_stats['Total_Projects']):,}")
print(f"• Successful projects: {int(best_stats['Successful_Projects']):,}")
print(f"• Success rate: {best_stats['Success_Rate']:.2f}%")
print(f"• Advantage over overall average: {best_stats['Success_Rate_Difference']:+.2f} percentage points")
print(f"• Share of all projects: {best_stats['Project_Share']:.2f}%")

# Rank all specified categories
print(f"\n🏆 RANKING OF SPECIFIED CATEGORIES:")
ranked_categories = specified_analysis.sort_values('Success_Rate', ascending=False)
for i, (category, row) in enumerate(ranked_categories.iterrows(), 1):
    success_rate = row['Success_Rate']
    total = int(row['Total_Projects'])
    print(f"{i}. {category}: {success_rate:.2f}% ({total:,} projects)")

# Statistical significance testing
from scipy.stats import chi2_contingency

# Create contingency table for the specified categories
contingency_data = []
category_names = []

for category in specified_categories:
    if category in specified_analysis.index:
        row = specified_analysis.loc[category]
        successful = int(row['Successful_Projects'])
        failed = int(row['Failed_Projects'])
        contingency_data.append([successful, failed])
        category_names.append(category)

# Perform chi-square test
if len(contingency_data) > 1:
    chi2, p_value, dof, expected = chi2_contingency(contingency_data)
    
    print(f"\n📈 STATISTICAL SIGNIFICANCE TEST:")
    print(f"Chi-square statistic: {chi2:.4f}")
    print(f"P-value: {p_value:.6f}")
    print(f"Degrees of freedom: {dof}")
    
    if p_value < 0.05:
        print(f"\n✅ RESULT: The differences in success rates are statistically significant (p < 0.05)")
        print(f"This means the launch length does significantly affect success rate.")
    else:
        print(f"\n❌ RESULT: The differences in success rates are NOT statistically significant (p >= 0.05)")
        print(f"This means launch length may not significantly affect success rate.")

# Create comprehensive visualizations
fig, axes = plt.subplots(2, 2, figsize=(20, 16))

# 1. Bar chart of success rates by duration category
ax1 = axes[0, 0]
categories = [cat.split('. ')[1] for cat in specified_categories]  # Remove letter prefixes for cleaner display
success_rates = [specified_analysis.loc[cat]['Success_Rate'] for cat in specified_categories]
colors = ['gold' if rate == max(success_rates) else 'skyblue' for rate in success_rates]

bars1 = ax1.bar(categories, success_rates, color=colors, alpha=0.8, edgecolor='navy')
ax1.axhline(y=overall_success_rate, color='red', linestyle='--', linewidth=2, 
           label=f'Overall Average ({overall_success_rate:.1f}%)')
ax1.set_ylabel('Success Rate (%)')
ax1.set_title('Success Rate by Campaign Duration', fontsize=14, fontweight='bold')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Add value labels on bars
for bar, rate in zip(bars1, success_rates):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
             f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')

# 2. Project count by duration category
ax2 = axes[0, 1]
project_counts = [specified_analysis.loc[cat]['Total_Projects'] for cat in specified_categories]
bars2 = ax2.bar(categories, project_counts, color='lightgreen', alpha=0.8, edgecolor='darkgreen')
ax2.set_ylabel('Number of Projects')
ax2.set_title('Project Count by Campaign Duration', fontsize=14, fontweight='bold')
ax2.grid(True, alpha=0.3)

# Add value labels on bars
for bar, count in zip(bars2, project_counts):
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height + max(project_counts)*0.01,
             f'{int(count):,}', ha='center', va='bottom', fontweight='bold')

# 3. Success vs Failure breakdown
ax3 = axes[1, 0]
successful_counts = [specified_analysis.loc[cat]['Successful_Projects'] for cat in specified_categories]
failed_counts = [specified_analysis.loc[cat]['Failed_Projects'] for cat in specified_categories]

x_pos = np.arange(len(categories))
width = 0.35

bars3a = ax3.bar(x_pos - width/2, successful_counts, width, label='Successful', 
                color='green', alpha=0.8)
bars3b = ax3.bar(x_pos + width/2, failed_counts, width, label='Failed', 
                color='red', alpha=0.8)

ax3.set_ylabel('Number of Projects')
ax3.set_title('Successful vs Failed Projects by Duration', fontsize=14, fontweight='bold')
ax3.set_xticks(x_pos)
ax3.set_xticklabels(categories)
ax3.legend()
ax3.grid(True, alpha=0.3)

# 4. Distribution of all campaign durations (histogram)
ax4 = axes[1, 1]
# Filter to reasonable range for visualization
duration_for_hist = df[df['Campaign_Duration'] <= 100]['Campaign_Duration']
ax4.hist(duration_for_hist, bins=50, color='purple', alpha=0.7, edgecolor='black')
ax4.axvline(x=10.5, color='red', linestyle='--', alpha=0.7, label='Category boundaries')
ax4.axvline(x=20.5, color='red', linestyle='--', alpha=0.7)
ax4.axvline(x=30.5, color='red', linestyle='--', alpha=0.7)
ax4.axvline(x=40.5, color='red', linestyle='--', alpha=0.7)
ax4.set_xlabel('Campaign Duration (days)')
ax4.set_ylabel('Number of Projects')
ax4.set_title('Distribution of Campaign Durations', fontsize=14, fontweight='bold')
ax4.legend()
ax4.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Analyze success rate by category for each duration range
print("📊 SUCCESS RATE BY CATEGORY FOR EACH DURATION RANGE:")
print("=" * 70)

for duration_cat in specified_categories:
    if duration_cat in df['Duration_Category'].values:
        subset = df[df['Duration_Category'] == duration_cat]
        category_success = subset.groupby('Category').agg({
            'State': ['count', lambda x: (x == 'Successful').sum()]
        })
        category_success.columns = ['Total', 'Successful']
        category_success['Success_Rate'] = (category_success['Successful'] / category_success['Total']) * 100
        category_success = category_success[category_success['Total'] >= 10]  # Min 10 projects
        category_success = category_success.sort_values('Success_Rate', ascending=False)
        
        print(f"\n{duration_cat}:")
        print(f"{'Category':<15} {'Total':<6} {'Success%':<8}")
        print("-" * 35)
        
        for category, row in category_success.head(5).iterrows():
            total = int(row['Total'])
            success_rate = row['Success_Rate']
            print(f"{category:<15} {total:<6} {success_rate:<8.1f}")

# Analyze goal amount impact within each duration category
print("💰 GOAL AMOUNT ANALYSIS BY DURATION CATEGORY:")
print("=" * 60)

for duration_cat in specified_categories:
    if duration_cat in df['Duration_Category'].values:
        subset = df[df['Duration_Category'] == duration_cat]
        
        # Create goal ranges
        subset_copy = subset.copy()
        subset_copy['Goal_Range'] = pd.cut(subset_copy['Goal'], 
                                         bins=[0, 1000, 5000, 10000, 50000, float('inf')],
                                         labels=['<$1K', '$1K-5K', '$5K-10K', '$10K-50K', '>$50K'])
        
        goal_analysis = subset_copy.groupby('Goal_Range').agg({
            'State': ['count', lambda x: (x == 'Successful').sum()]
        })
        goal_analysis.columns = ['Total', 'Successful']
        goal_analysis['Success_Rate'] = (goal_analysis['Successful'] / goal_analysis['Total']) * 100
        
        print(f"\n{duration_cat}:")
        avg_goal = subset['Goal'].mean()
        median_goal = subset['Goal'].median()
        print(f"Average goal: ${avg_goal:,.0f} | Median goal: ${median_goal:,.0f}")
        
        print(f"{'Goal Range':<10} {'Total':<6} {'Success%':<8}")
        print("-" * 30)
        for goal_range, row in goal_analysis.iterrows():
            if row['Total'] >= 5:  # Min 5 projects
                total = int(row['Total'])
                success_rate = row['Success_Rate']
                print(f"{goal_range:<10} {total:<6} {success_rate:<8.1f}")

# Detailed comparison of top 2 performing duration categories
top_2_categories = specified_analysis.nlargest(2, 'Success_Rate')

print("🔍 DETAILED COMPARISON OF TOP 2 DURATION CATEGORIES:")
print("=" * 65)

comparison_metrics = []
for i, (category, row) in enumerate(top_2_categories.iterrows(), 1):
    subset = df[df['Duration_Category'] == category]
    
    # Calculate additional metrics
    avg_goal = subset['Goal'].mean()
    avg_pledged = subset['Pledged'].mean()
    avg_backers = subset['Backers'].mean()
    successful_subset = subset[subset['State'] == 'Successful']
    avg_successful_goal = successful_subset['Goal'].mean() if len(successful_subset) > 0 else 0
    
    print(f"\n{i}. {category}:")
    print(f"   • Success rate: {row['Success_Rate']:.2f}%")
    print(f"   • Total projects: {int(row['Total_Projects']):,}")
    print(f"   • Average goal: ${avg_goal:,.0f}")
    print(f"   • Average pledged: ${avg_pledged:,.0f}")
    print(f"   • Average backers: {avg_backers:.0f}")
    print(f"   • Average successful project goal: ${avg_successful_goal:,.0f}")
    
    comparison_metrics.append({
        'category': category,
        'success_rate': row['Success_Rate'],
        'avg_goal': avg_goal,
        'avg_pledged': avg_pledged,
        'avg_backers': avg_backers
    })

# Compare the metrics
if len(comparison_metrics) == 2:
    cat1, cat2 = comparison_metrics[0], comparison_metrics[1]
    print(f"\n📈 KEY DIFFERENCES:")
    print(f"• Success rate difference: {cat1['success_rate'] - cat2['success_rate']:+.2f} percentage points")
    print(f"• Average goal difference: ${cat1['avg_goal'] - cat2['avg_goal']:+,.0f}")
    print(f"• Average pledged difference: ${cat1['avg_pledged'] - cat2['avg_pledged']:+,.0f}")
    print(f"• Average backers difference: {cat1['avg_backers'] - cat2['avg_backers']:+.0f}")

# Final summary and insights
print("=" * 80)
print("🎯 FINAL SUMMARY: LAUNCH LENGTH SUCCESS RATE ANALYSIS")
print("=" * 80)

print(f"\n🏆 MAIN ANSWER:")
print(f"'{best_category}' has the highest success rate at {best_success_rate:.2f}%")

print(f"\n📊 COMPLETE RANKING:")
for i, (category, row) in enumerate(ranked_categories.iterrows(), 1):
    success_rate = row['Success_Rate']
    total = int(row['Total_Projects'])
    difference = row['Success_Rate_Difference']
    print(f"{i}. {category}: {success_rate:.2f}% ({total:,} projects, {difference:+.2f}pp vs average)")

print(f"\n📈 KEY INSIGHTS:")

# Determine patterns
short_term = specified_analysis.loc[['a. 1-10 days', 'b. 11-20 days']]['Success_Rate'].mean()
medium_term = specified_analysis.loc['c. 21-30 days']['Success_Rate']
long_term = specified_analysis.loc['d. >40 days']['Success_Rate']

print(f"• Short campaigns (1-20 days): {short_term:.2f}% average success rate")
print(f"• Medium campaigns (21-30 days): {medium_term:.2f}% success rate")
print(f"• Long campaigns (>40 days): {long_term:.2f}% success rate")

if short_term > medium_term and short_term > long_term:
    print(f"• Pattern: Shorter campaigns tend to be more successful")
elif long_term > short_term and long_term > medium_term:
    print(f"• Pattern: Longer campaigns tend to be more successful")
else:
    print(f"• Pattern: Medium-length campaigns perform best")

# Statistical significance insight
if 'p_value' in locals():
    if p_value < 0.05:
        print(f"• The differences are statistically significant (p = {p_value:.4f})")
        print(f"• Campaign duration does significantly impact success rate")
    else:
        print(f"• The differences are not statistically significant (p = {p_value:.4f})")
        print(f"• Campaign duration may not significantly impact success rate")

print(f"\n💡 STRATEGIC RECOMMENDATIONS:")
best_letter = best_category.split('.')[0]
print(f"• Choose {best_category.split('. ')[1]} for optimal success rate")
print(f"• This duration range shows {best_stats['Success_Rate_Difference']:+.2f}pp advantage over average")
print(f"• Consider your project type and funding goal when selecting duration")

if best_success_rate > 40:
    print(f"• The winning category has a strong success rate, making it a reliable choice")
elif best_success_rate > 35:
    print(f"• The winning category has a good success rate, but consider other factors too")
else:
    print(f"• Even the best duration has moderate success rate - focus on other success factors")

print(f"\n📊 METHODOLOGY:")
print(f"• Campaign duration calculated as days between launch and deadline")
print(f"• Categories defined as specified: 1-10, 11-20, 21-30, >40 days")
print(f"• Success rate = (Successful projects / Total projects) × 100")
print(f"• Statistical significance tested using chi-square test")
print(f"• Analysis includes {len(df):,} valid projects")

print(f"\n🎯 ANSWER TO QUESTION:")
answer_letter = best_category.split('.')[0]
print(f"The launch length with the highest success rate is: {answer_letter}. {best_category.split('. ')[1]}")